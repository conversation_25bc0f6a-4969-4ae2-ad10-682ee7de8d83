"use strict";

exports._ = exports._extends = _extends;
function _extends() {
    exports._ = exports._extends = _extends = Object.assign || function assign(target) {
        for (var i = 1; i < arguments.length; i++) {
            var source = arguments[i];
            for (var key in source) if (Object.prototype.hasOwnProperty.call(source, key)) target[key] = source[key];
        }

        return target;
    };

    return _extends.apply(this, arguments);
}
