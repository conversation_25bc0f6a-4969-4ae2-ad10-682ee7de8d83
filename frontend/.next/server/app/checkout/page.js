/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/checkout/page";
exports.ids = ["app/checkout/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcheckout%2Fpage&page=%2Fcheckout%2Fpage&appPaths=%2Fcheckout%2Fpage&pagePath=private-next-app-dir%2Fcheckout%2Fpage.tsx&appDir=%2FUsers%2Flinjinbin%2FProjects%2Fpaypal-demo%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Flinjinbin%2FProjects%2Fpaypal-demo%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcheckout%2Fpage&page=%2Fcheckout%2Fpage&appPaths=%2Fcheckout%2Fpage&pagePath=private-next-app-dir%2Fcheckout%2Fpage.tsx&appDir=%2FUsers%2Flinjinbin%2FProjects%2Fpaypal-demo%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Flinjinbin%2FProjects%2Fpaypal-demo%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'checkout',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/checkout/page.tsx */ \"(rsc)/./src/app/checkout/page.tsx\")), \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/checkout/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/checkout/page\",\n        pathname: \"/checkout\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcheckout%2Fpage&page=%2Fcheckout%2Fpage&appPaths=%2Fcheckout%2Fpage&pagePath=private-next-app-dir%2Fcheckout%2Fpage.tsx&appDir=%2FUsers%2Flinjinbin%2FProjects%2Fpaypal-demo%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Flinjinbin%2FProjects%2Fpaypal-demo%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Flinjinbin%2FProjects%2Fpaypal-demo%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Flinjinbin%2FProjects%2Fpaypal-demo%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Flinjinbin%2FProjects%2Fpaypal-demo%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Flinjinbin%2FProjects%2Fpaypal-demo%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Flinjinbin%2FProjects%2Fpaypal-demo%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Flinjinbin%2FProjects%2Fpaypal-demo%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Flinjinbin%2FProjects%2Fpaypal-demo%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Flinjinbin%2FProjects%2Fpaypal-demo%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Flinjinbin%2FProjects%2Fpaypal-demo%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Flinjinbin%2FProjects%2Fpaypal-demo%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Flinjinbin%2FProjects%2Fpaypal-demo%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Flinjinbin%2FProjects%2Fpaypal-demo%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Flinjinbin%2FProjects%2Fpaypal-demo%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Flinjinbin%2FProjects%2Fpaypal-demo%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Flinjinbin%2FProjects%2Fpaypal-demo%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Flinjinbin%2FProjects%2Fpaypal-demo%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Flinjinbin%2FProjects%2Fpaypal-demo%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Flinjinbin%2FProjects%2Fpaypal-demo%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Flinjinbin%2FProjects%2Fpaypal-demo%2Ffrontend%2Fsrc%2Fapp%2Fcheckout%2Fpage.tsx&server=true!":
/*!*********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Flinjinbin%2FProjects%2Fpaypal-demo%2Ffrontend%2Fsrc%2Fapp%2Fcheckout%2Fpage.tsx&server=true! ***!
  \*********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/checkout/page.tsx */ \"(ssr)/./src/app/checkout/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZsaW5qaW5iaW4lMkZQcm9qZWN0cyUyRnBheXBhbC1kZW1vJTJGZnJvbnRlbmQlMkZzcmMlMkZhcHAlMkZjaGVja291dCUyRnBhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL3BheXBhbC1kZW1vLWZyb250ZW5kLz82MDA2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2xpbmppbmJpbi9Qcm9qZWN0cy9wYXlwYWwtZGVtby9mcm9udGVuZC9zcmMvYXBwL2NoZWNrb3V0L3BhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Flinjinbin%2FProjects%2Fpaypal-demo%2Ffrontend%2Fsrc%2Fapp%2Fcheckout%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Flinjinbin%2FProjects%2Fpaypal-demo%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css&server=true!":
/*!*************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Flinjinbin%2FProjects%2Fpaypal-demo%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css&server=true! ***!
  \*************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/checkout/page.tsx":
/*!***********************************!*\
  !*** ./src/app/checkout/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Checkout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_PayPalButton__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/PayPalButton */ \"(ssr)/./src/components/PayPalButton.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n// 工具函数：确保金额保留2位小数\nconst roundToTwoDecimals = (amount)=>{\n    return Math.round(amount * 100) / 100;\n};\nfunction CheckoutContent() {\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const [product, setProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [paymentProcessing, setPaymentProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 从URL参数获取产品信息\n        const productId = searchParams.get(\"productId\");\n        const price = searchParams.get(\"price\");\n        const name = searchParams.get(\"name\");\n        if (!productId || !price || !name) {\n            setError(\"缺少必要的产品信息，请从商品页面重新选择。\");\n            setLoading(false);\n            return;\n        }\n        const priceNum = parseFloat(price);\n        if (isNaN(priceNum) || priceNum <= 0) {\n            setError(\"无效的价格信息。\");\n            setLoading(false);\n            return;\n        }\n        setProduct({\n            id: productId,\n            name: decodeURIComponent(name),\n            price: priceNum,\n            description: `商品ID: ${productId}`\n        });\n        setLoading(false);\n    }, [\n        searchParams\n    ]);\n    const handlePaymentSuccess = (orderData)=>{\n        console.log(\"支付成功:\", orderData);\n        setPaymentProcessing(false);\n    // PayPalButton组件会自动重定向到成功页面\n    };\n    const handlePaymentError = (error)=>{\n        console.error(\"支付失败:\", error);\n        setError(error);\n        setPaymentProcessing(false);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-64\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"ml-2\",\n                    children: \"加载中...\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n            lineNumber: 70,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-2xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5 mr-2\",\n                                fill: \"currentColor\",\n                                viewBox: \"0 0 20 20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                        href: \"/\",\n                        className: \"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium\",\n                        children: \"返回首页\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, this);\n    }\n    if (!product) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-2xl mx-auto text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 mb-4\",\n                    children: \"未找到产品信息\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                    href: \"/\",\n                    className: \"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium\",\n                    children: \"返回首页\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n            lineNumber: 102,\n            columnNumber: 7\n        }, this);\n    }\n    // 计算费用\n    const subtotal = roundToTwoDecimals(product.price);\n    const tax = roundToTwoDecimals(subtotal * 0.08); // 8% 税率，保留2位小数\n    const total = roundToTwoDecimals(subtotal + tax); // 总计保留2位小数\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-4xl mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-gray-900 mb-2\",\n                        children: \"结账\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"请确认您的订单信息并完成支付\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-md p-6 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: \"商品信息\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-20 h-20 bg-gray-200 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-8 h-8 text-gray-500\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                                    lineNumber: 135,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900\",\n                                                        children: product.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                                        lineNumber: 140,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: product.description\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-semibold text-green-600 mt-2\",\n                                                        children: [\n                                                            \"$\",\n                                                            product.price.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 border border-blue-200 rounded-lg p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-blue-800 mb-4\",\n                                        children: \"支付流程\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-blue-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center mr-3 text-xs font-bold\",\n                                                        children: \"1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"点击PayPal支付按钮\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-blue-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center mr-3 text-xs font-bold\",\n                                                        children: \"2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"在PayPal页面完成支付\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-blue-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center mr-3 text-xs font-bold\",\n                                                        children: \"3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"系统自动处理订单\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-blue-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center mr-3 text-xs font-bold\",\n                                                        children: \"4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                                        lineNumber: 170,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"重定向到成功页面\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-md p-6 sticky top-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold mb-4\",\n                                    children: \"订单摘要\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"商品小计\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: [\n                                                        \"$\",\n                                                        subtotal.toFixed(2)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"税费 (8%)\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: [\n                                                        \"$\",\n                                                        tax.toFixed(2)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: \"总计\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg font-bold text-green-600\",\n                                                        children: [\n                                                            \"$\",\n                                                            total.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PayPalButton__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        amount: parseFloat(total.toFixed(2)),\n                                        productId: product.id,\n                                        onSuccess: handlePaymentSuccess,\n                                        onError: handlePaymentError\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-500 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 mr-1\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"安全支付\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"您的支付信息通过PayPal安全处理\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 pt-4 border-t\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/\",\n                                        className: \"w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors font-medium text-center block\",\n                                        children: \"继续购物\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this),\n            paymentProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-700\",\n                            children: \"正在处理支付...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                lineNumber: 238,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n        lineNumber: 120,\n        columnNumber: 5\n    }, this);\n}\nfunction Checkout() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-64\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 9\n                }, void 0),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"ml-2\",\n                    children: \"Loading...\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 9\n                }, void 0)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n            lineNumber: 252,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CheckoutContent, {}, void 0, false, {\n            fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n            lineNumber: 257,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx\",\n        lineNumber: 251,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2NoZWNrb3V0L3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBRXNEO0FBQ0o7QUFDckI7QUFDd0I7QUFVckQsa0JBQWtCO0FBQ2xCLE1BQU1NLHFCQUFxQixDQUFDQztJQUMxQixPQUFPQyxLQUFLQyxLQUFLLENBQUNGLFNBQVMsT0FBTztBQUNwQztBQUVBLFNBQVNHO0lBQ1AsTUFBTUMsZUFBZVIsZ0VBQWVBO0lBQ3BDLE1BQU0sQ0FBQ1MsU0FBU0MsV0FBVyxHQUFHWCwrQ0FBUUEsQ0FBcUI7SUFDM0QsTUFBTSxDQUFDWSxTQUFTQyxXQUFXLEdBQUdiLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ2MsT0FBT0MsU0FBUyxHQUFHZiwrQ0FBUUEsQ0FBZ0I7SUFDbEQsTUFBTSxDQUFDZ0IsbUJBQW1CQyxxQkFBcUIsR0FBR2pCLCtDQUFRQSxDQUFDO0lBRTNERCxnREFBU0EsQ0FBQztRQUNSLGVBQWU7UUFDZixNQUFNbUIsWUFBWVQsYUFBYVUsR0FBRyxDQUFDO1FBQ25DLE1BQU1DLFFBQVFYLGFBQWFVLEdBQUcsQ0FBQztRQUMvQixNQUFNRSxPQUFPWixhQUFhVSxHQUFHLENBQUM7UUFFOUIsSUFBSSxDQUFDRCxhQUFhLENBQUNFLFNBQVMsQ0FBQ0MsTUFBTTtZQUNqQ04sU0FBUztZQUNURixXQUFXO1lBQ1g7UUFDRjtRQUVBLE1BQU1TLFdBQVdDLFdBQVdIO1FBQzVCLElBQUlJLE1BQU1GLGFBQWFBLFlBQVksR0FBRztZQUNwQ1AsU0FBUztZQUNURixXQUFXO1lBQ1g7UUFDRjtRQUVBRixXQUFXO1lBQ1RjLElBQUlQO1lBQ0pHLE1BQU1LLG1CQUFtQkw7WUFDekJELE9BQU9FO1lBQ1BLLGFBQWEsQ0FBQyxNQUFNLEVBQUVULFVBQVUsQ0FBQztRQUNuQztRQUNBTCxXQUFXO0lBQ2IsR0FBRztRQUFDSjtLQUFhO0lBRWpCLE1BQU1tQix1QkFBdUIsQ0FBQ0M7UUFDNUJDLFFBQVFDLEdBQUcsQ0FBQyxTQUFTRjtRQUNyQloscUJBQXFCO0lBQ3JCLDRCQUE0QjtJQUM5QjtJQUVBLE1BQU1lLHFCQUFxQixDQUFDbEI7UUFDMUJnQixRQUFRaEIsS0FBSyxDQUFDLFNBQVNBO1FBQ3ZCQyxTQUFTRDtRQUNURyxxQkFBcUI7SUFDdkI7SUFFQSxJQUFJTCxTQUFTO1FBQ1gscUJBQ0UsOERBQUNxQjtZQUFJQyxXQUFVOzs4QkFDYiw4REFBQ0Q7b0JBQUlDLFdBQVU7Ozs7Ozs4QkFDZiw4REFBQ0M7b0JBQUtELFdBQVU7OEJBQU87Ozs7Ozs7Ozs7OztJQUc3QjtJQUVBLElBQUlwQixPQUFPO1FBQ1QscUJBQ0UsOERBQUNtQjtZQUFJQyxXQUFVOzs4QkFDYiw4REFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0U7Z0NBQUlGLFdBQVU7Z0NBQWVHLE1BQUs7Z0NBQWVDLFNBQVE7MENBQ3hELDRFQUFDQztvQ0FBS0MsVUFBUztvQ0FBVUMsR0FBRTtvQ0FBb0hDLFVBQVM7Ozs7Ozs7Ozs7OzBDQUUxSiw4REFBQ1A7MENBQU1yQjs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBR1gsOERBQUNtQjtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ2hDLGtEQUFJQTt3QkFDSHlDLE1BQUs7d0JBQ0xULFdBQVU7a0NBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBTVQ7SUFFQSxJQUFJLENBQUN4QixTQUFTO1FBQ1oscUJBQ0UsOERBQUN1QjtZQUFJQyxXQUFVOzs4QkFDYiw4REFBQ1U7b0JBQUVWLFdBQVU7OEJBQXFCOzs7Ozs7OEJBQ2xDLDhEQUFDaEMsa0RBQUlBO29CQUNIeUMsTUFBSztvQkFDTFQsV0FBVTs4QkFDWDs7Ozs7Ozs7Ozs7O0lBS1A7SUFFQSxPQUFPO0lBQ1AsTUFBTVcsV0FBV3pDLG1CQUFtQk0sUUFBUVUsS0FBSztJQUNqRCxNQUFNMEIsTUFBTTFDLG1CQUFtQnlDLFdBQVcsT0FBTyxlQUFlO0lBQ2hFLE1BQU1FLFFBQVEzQyxtQkFBbUJ5QyxXQUFXQyxNQUFNLFdBQVc7SUFFN0QscUJBQ0UsOERBQUNiO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNjO3dCQUFHZCxXQUFVO2tDQUF3Qzs7Ozs7O2tDQUN0RCw4REFBQ1U7d0JBQUVWLFdBQVU7a0NBQWdCOzs7Ozs7Ozs7Ozs7MEJBRy9CLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBRWIsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FFYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDZTt3Q0FBR2YsV0FBVTtrREFBNkI7Ozs7OztrREFDM0MsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7MERBQ2IsNEVBQUNFO29EQUFJRixXQUFVO29EQUF3QkcsTUFBSztvREFBZUMsU0FBUTs4REFDakUsNEVBQUNDO3dEQUFLQyxVQUFTO3dEQUFVQyxHQUFFO3dEQUE2RkMsVUFBUzs7Ozs7Ozs7Ozs7Ozs7OzswREFHckksOERBQUNUO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ2dCO3dEQUFHaEIsV0FBVTtrRUFBcUN4QixRQUFRVyxJQUFJOzs7Ozs7a0VBQy9ELDhEQUFDdUI7d0RBQUVWLFdBQVU7a0VBQWlCeEIsUUFBUWlCLFdBQVc7Ozs7OztrRUFDakQsOERBQUNpQjt3REFBRVYsV0FBVTs7NERBQTRDOzREQUFFeEIsUUFBUVUsS0FBSyxDQUFDK0IsT0FBTyxDQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQU12Riw4REFBQ2xCO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ2dCO3dDQUFHaEIsV0FBVTtrREFBMkM7Ozs7OztrREFDekQsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRDt3REFBSUMsV0FBVTtrRUFBc0c7Ozs7OztvREFFL0c7Ozs7Ozs7MERBR1IsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0Q7d0RBQUlDLFdBQVU7a0VBQXNHOzs7Ozs7b0RBRS9HOzs7Ozs7OzBEQUdSLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNEO3dEQUFJQyxXQUFVO2tFQUFzRzs7Ozs7O29EQUUvRzs7Ozs7OzswREFHUiw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRDt3REFBSUMsV0FBVTtrRUFBc0c7Ozs7OztvREFFL0c7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBUWQsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNlO29DQUFHZixXQUFVOzhDQUE2Qjs7Ozs7OzhDQUczQyw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNDO29EQUFLRCxXQUFVOzhEQUFnQjs7Ozs7OzhEQUNoQyw4REFBQ0M7b0RBQUtELFdBQVU7O3dEQUFjO3dEQUFFVyxTQUFTTSxPQUFPLENBQUM7Ozs7Ozs7Ozs7Ozs7c0RBRW5ELDhEQUFDbEI7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDQztvREFBS0QsV0FBVTs4REFBZ0I7Ozs7Ozs4REFDaEMsOERBQUNDO29EQUFLRCxXQUFVOzt3REFBYzt3REFBRVksSUFBSUssT0FBTyxDQUFDOzs7Ozs7Ozs7Ozs7O3NEQUU5Qyw4REFBQ2xCOzRDQUFJQyxXQUFVO3NEQUNiLDRFQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNDO3dEQUFLRCxXQUFVO2tFQUF3Qjs7Ozs7O2tFQUN4Qyw4REFBQ0M7d0RBQUtELFdBQVU7OzREQUFtQzs0REFBRWEsTUFBTUksT0FBTyxDQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBTXpFLDhEQUFDbEI7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUMvQixnRUFBWUE7d0NBQ1hFLFFBQVFrQixXQUFXd0IsTUFBTUksT0FBTyxDQUFDO3dDQUNqQ2pDLFdBQVdSLFFBQVFlLEVBQUU7d0NBQ3JCMkIsV0FBV3hCO3dDQUNYeUIsU0FBU3JCOzs7Ozs7Ozs7Ozs4Q0FLYiw4REFBQ0M7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNFO29EQUFJRixXQUFVO29EQUFlRyxNQUFLO29EQUFlQyxTQUFROzhEQUN4RCw0RUFBQ0M7d0RBQUtDLFVBQVM7d0RBQVVDLEdBQUU7d0RBQXlHQyxVQUFTOzs7Ozs7Ozs7OztnREFDekk7Ozs7Ozs7c0RBR1IsOERBQUNFO3NEQUFFOzs7Ozs7Ozs7Ozs7OENBSUwsOERBQUNYO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDaEMsa0RBQUlBO3dDQUNIeUMsTUFBSzt3Q0FDTFQsV0FBVTtrREFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQVNSbEIsbUNBQ0MsOERBQUNpQjtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7Ozs7O3NDQUNmLDhEQUFDVTs0QkFBRVYsV0FBVTtzQ0FBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTXpDO0FBRWUsU0FBU29CO0lBQ3RCLHFCQUNFLDhEQUFDeEQsMkNBQVFBO1FBQUN5RCx3QkFDUiw4REFBQ3RCO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDRDtvQkFBSUMsV0FBVTs7Ozs7OzhCQUNmLDhEQUFDQztvQkFBS0QsV0FBVTs4QkFBTzs7Ozs7Ozs7Ozs7O2tCQUd6Qiw0RUFBQzFCOzs7Ozs7Ozs7O0FBR1AiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wYXlwYWwtZGVtby1mcm9udGVuZC8uL3NyYy9hcHAvY2hlY2tvdXQvcGFnZS50c3g/M2ViMyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IFN1c3BlbnNlLCB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlU2VhcmNoUGFyYW1zIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcbmltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluayc7XG5pbXBvcnQgUGF5UGFsQnV0dG9uIGZyb20gJ0AvY29tcG9uZW50cy9QYXlQYWxCdXR0b24nO1xuaW1wb3J0IHsgUGF5UGFsQ2FwdHVyZU9yZGVyUmVzcG9uc2UgfSBmcm9tICdAL3R5cGVzL3BheXBhbCc7XG5cbmludGVyZmFjZSBQcm9kdWN0SW5mbyB7XG4gIGlkOiBzdHJpbmc7XG4gIG5hbWU6IHN0cmluZztcbiAgcHJpY2U6IG51bWJlcjtcbiAgZGVzY3JpcHRpb24/OiBzdHJpbmc7XG59XG5cbi8vIOW3peWFt+WHveaVsO+8muehruS/nemHkemineS/neeVmTLkvY3lsI/mlbBcbmNvbnN0IHJvdW5kVG9Ud29EZWNpbWFscyA9IChhbW91bnQ6IG51bWJlcik6IG51bWJlciA9PiB7XG4gIHJldHVybiBNYXRoLnJvdW5kKGFtb3VudCAqIDEwMCkgLyAxMDA7XG59O1xuXG5mdW5jdGlvbiBDaGVja291dENvbnRlbnQoKSB7XG4gIGNvbnN0IHNlYXJjaFBhcmFtcyA9IHVzZVNlYXJjaFBhcmFtcygpO1xuICBjb25zdCBbcHJvZHVjdCwgc2V0UHJvZHVjdF0gPSB1c2VTdGF0ZTxQcm9kdWN0SW5mbyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW3BheW1lbnRQcm9jZXNzaW5nLCBzZXRQYXltZW50UHJvY2Vzc2luZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyDku45VUkzlj4LmlbDojrflj5bkuqflk4Hkv6Hmga9cbiAgICBjb25zdCBwcm9kdWN0SWQgPSBzZWFyY2hQYXJhbXMuZ2V0KCdwcm9kdWN0SWQnKTtcbiAgICBjb25zdCBwcmljZSA9IHNlYXJjaFBhcmFtcy5nZXQoJ3ByaWNlJyk7XG4gICAgY29uc3QgbmFtZSA9IHNlYXJjaFBhcmFtcy5nZXQoJ25hbWUnKTtcblxuICAgIGlmICghcHJvZHVjdElkIHx8ICFwcmljZSB8fCAhbmFtZSkge1xuICAgICAgc2V0RXJyb3IoJ+e8uuWwkeW/heimgeeahOS6p+WTgeS/oeaBr++8jOivt+S7juWVhuWTgemhtemdoumHjeaWsOmAieaLqeOAgicpO1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgY29uc3QgcHJpY2VOdW0gPSBwYXJzZUZsb2F0KHByaWNlKTtcbiAgICBpZiAoaXNOYU4ocHJpY2VOdW0pIHx8IHByaWNlTnVtIDw9IDApIHtcbiAgICAgIHNldEVycm9yKCfml6DmlYjnmoTku7fmoLzkv6Hmga/jgIInKTtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIHNldFByb2R1Y3Qoe1xuICAgICAgaWQ6IHByb2R1Y3RJZCxcbiAgICAgIG5hbWU6IGRlY29kZVVSSUNvbXBvbmVudChuYW1lKSxcbiAgICAgIHByaWNlOiBwcmljZU51bSxcbiAgICAgIGRlc2NyaXB0aW9uOiBg5ZWG5ZOBSUQ6ICR7cHJvZHVjdElkfWBcbiAgICB9KTtcbiAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgfSwgW3NlYXJjaFBhcmFtc10pO1xuXG4gIGNvbnN0IGhhbmRsZVBheW1lbnRTdWNjZXNzID0gKG9yZGVyRGF0YTogUGF5UGFsQ2FwdHVyZU9yZGVyUmVzcG9uc2UpID0+IHtcbiAgICBjb25zb2xlLmxvZygn5pSv5LuY5oiQ5YqfOicsIG9yZGVyRGF0YSk7XG4gICAgc2V0UGF5bWVudFByb2Nlc3NpbmcoZmFsc2UpO1xuICAgIC8vIFBheVBhbEJ1dHRvbue7hOS7tuS8muiHquWKqOmHjeWumuWQkeWIsOaIkOWKn+mhtemdolxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVBheW1lbnRFcnJvciA9IChlcnJvcjogc3RyaW5nKSA9PiB7XG4gICAgY29uc29sZS5lcnJvcign5pSv5LuY5aSx6LSlOicsIGVycm9yKTtcbiAgICBzZXRFcnJvcihlcnJvcik7XG4gICAgc2V0UGF5bWVudFByb2Nlc3NpbmcoZmFsc2UpO1xuICB9O1xuXG4gIGlmIChsb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbWluLWgtNjRcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtOCB3LTggYm9yZGVyLWItMiBib3JkZXItYmx1ZS02MDBcIj48L2Rpdj5cbiAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibWwtMlwiPuWKoOi9veS4rS4uLjwvc3Bhbj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICBpZiAoZXJyb3IpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy0yeGwgbXgtYXV0b1wiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXJlZC0xMDAgYm9yZGVyIGJvcmRlci1yZWQtNDAwIHRleHQtcmVkLTcwMCBweC00IHB5LTMgcm91bmRlZCBtYi00XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTUgaC01IG1yLTJcIiBmaWxsPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyMCAyMFwiPlxuICAgICAgICAgICAgICA8cGF0aCBmaWxsUnVsZT1cImV2ZW5vZGRcIiBkPVwiTTE4IDEwYTggOCAwIDExLTE2IDAgOCA4IDAgMDExNiAwem0tNyA0YTEgMSAwIDExLTIgMCAxIDEgMCAwMTIgMHptLTEtOWExIDEgMCAwMC0xIDF2NGExIDEgMCAxMDIgMFY2YTEgMSAwIDAwLTEtMXpcIiBjbGlwUnVsZT1cImV2ZW5vZGRcIiAvPlxuICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICA8c3Bhbj57ZXJyb3J9PC9zcGFuPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICBocmVmPVwiL1wiXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ibHVlLTYwMCB0ZXh0LXdoaXRlIHB4LTYgcHktMiByb3VuZGVkLWxnIGhvdmVyOmJnLWJsdWUtNzAwIHRyYW5zaXRpb24tY29sb3JzIGZvbnQtbWVkaXVtXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICDov5Tlm57pppbpobVcbiAgICAgICAgICA8L0xpbms+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIGlmICghcHJvZHVjdCkge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTJ4bCBteC1hdXRvIHRleHQtY2VudGVyXCI+XG4gICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbWItNFwiPuacquaJvuWIsOS6p+WTgeS/oeaBrzwvcD5cbiAgICAgICAgPExpbmtcbiAgICAgICAgICBocmVmPVwiL1wiXG4gICAgICAgICAgY2xhc3NOYW1lPVwiYmctYmx1ZS02MDAgdGV4dC13aGl0ZSBweC02IHB5LTIgcm91bmRlZC1sZyBob3ZlcjpiZy1ibHVlLTcwMCB0cmFuc2l0aW9uLWNvbG9ycyBmb250LW1lZGl1bVwiXG4gICAgICAgID5cbiAgICAgICAgICDov5Tlm57pppbpobVcbiAgICAgICAgPC9MaW5rPlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIC8vIOiuoeeul+i0ueeUqFxuICBjb25zdCBzdWJ0b3RhbCA9IHJvdW5kVG9Ud29EZWNpbWFscyhwcm9kdWN0LnByaWNlKTtcbiAgY29uc3QgdGF4ID0gcm91bmRUb1R3b0RlY2ltYWxzKHN1YnRvdGFsICogMC4wOCk7IC8vIDglIOeojueOh++8jOS/neeVmTLkvY3lsI/mlbBcbiAgY29uc3QgdG90YWwgPSByb3VuZFRvVHdvRGVjaW1hbHMoc3VidG90YWwgKyB0YXgpOyAvLyDmgLvorqHkv53nlZky5L2N5bCP5pWwXG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTR4bCBteC1hdXRvXCI+XG4gICAgICB7Lyog6aG16Z2i5qCH6aKYICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi04XCI+XG4gICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBtYi0yXCI+57uT6LSmPC9oMT5cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPuivt+ehruiupOaCqOeahOiuouWNleS/oeaBr+W5tuWujOaIkOaUr+S7mDwvcD5cbiAgICAgIDwvZGl2PlxuXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbGc6Z3JpZC1jb2xzLTMgZ2FwLThcIj5cbiAgICAgICAgey8qIOW3puS+p++8muiuouWNleS/oeaBryAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJsZzpjb2wtc3Bhbi0yXCI+XG4gICAgICAgICAgey8qIOWVhuWTgeS/oeaBryAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93LW1kIHAtNiBtYi02XCI+XG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIG1iLTRcIj7llYblk4Hkv6Hmga88L2gyPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTIwIGgtMjAgYmctZ3JheS0yMDAgcm91bmRlZC1sZyBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy04IGgtOCB0ZXh0LWdyYXktNTAwXCIgZmlsbD1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjAgMjBcIj5cbiAgICAgICAgICAgICAgICAgIDxwYXRoIGZpbGxSdWxlPVwiZXZlbm9kZFwiIGQ9XCJNNCAzYTIgMiAwIDAwLTIgMnYxMGEyIDIgMCAwMDIgMmgxMmEyIDIgMCAwMDItMlY1YTIgMiAwIDAwLTItMkg0em0xMiAxMkg0bDQtOCAzIDYgMi00IDMgNnpcIiBjbGlwUnVsZT1cImV2ZW5vZGRcIiAvPlxuICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwXCI+e3Byb2R1Y3QubmFtZX08L2gzPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj57cHJvZHVjdC5kZXNjcmlwdGlvbn08L3A+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JlZW4tNjAwIG10LTJcIj4ke3Byb2R1Y3QucHJpY2UudG9GaXhlZCgyKX08L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7Lyog5pSv5LuY5rWB56iL6K+05piOICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctYmx1ZS01MCBib3JkZXIgYm9yZGVyLWJsdWUtMjAwIHJvdW5kZWQtbGcgcC02XCI+XG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtYmx1ZS04MDAgbWItNFwiPuaUr+S7mOa1geeoizwvaDM+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTQgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHRleHQtYmx1ZS03MDBcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctNiBoLTYgYmctYmx1ZS02MDAgdGV4dC13aGl0ZSByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbXItMyB0ZXh0LXhzIGZvbnQtYm9sZFwiPlxuICAgICAgICAgICAgICAgICAgMVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIOeCueWHu1BheVBhbOaUr+S7mOaMiemSrlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciB0ZXh0LWJsdWUtNzAwXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTYgaC02IGJnLWJsdWUtNjAwIHRleHQtd2hpdGUgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG1yLTMgdGV4dC14cyBmb250LWJvbGRcIj5cbiAgICAgICAgICAgICAgICAgIDJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICDlnKhQYXlQYWzpobXpnaLlrozmiJDmlK/ku5hcbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgdGV4dC1ibHVlLTcwMFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy02IGgtNiBiZy1ibHVlLTYwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBtci0zIHRleHQteHMgZm9udC1ib2xkXCI+XG4gICAgICAgICAgICAgICAgICAzXG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAg57O757uf6Ieq5Yqo5aSE55CG6K6i5Y2VXG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHRleHQtYmx1ZS03MDBcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctNiBoLTYgYmctYmx1ZS02MDAgdGV4dC13aGl0ZSByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbXItMyB0ZXh0LXhzIGZvbnQtYm9sZFwiPlxuICAgICAgICAgICAgICAgICAgNFxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIOmHjeWumuWQkeWIsOaIkOWKn+mhtemdolxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7Lyog5Y+z5L6n77ya6K6i5Y2V5pGY6KaB5ZKM5pSv5LuYICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxnOmNvbC1zcGFuLTFcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93LW1kIHAtNiBzdGlja3kgdG9wLTRcIj5cbiAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgbWItNFwiPuiuouWNleaRmOimgTwvaDI+XG5cbiAgICAgICAgICAgIHsvKiDotLnnlKjmmI7nu4YgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMyBtYi02XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+5ZWG5ZOB5bCP6K6hPC9zcGFuPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+JHtzdWJ0b3RhbC50b0ZpeGVkKDIpfTwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+56iO6LS5ICg4JSk8L3NwYW4+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj4ke3RheC50b0ZpeGVkKDIpfTwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYm9yZGVyLXQgcHQtM1wiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZFwiPuaAu+iuoTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1ib2xkIHRleHQtZ3JlZW4tNjAwXCI+JHt0b3RhbC50b0ZpeGVkKDIpfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIFBheVBhbOaUr+S7mOaMiemSriAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNFwiPlxuICAgICAgICAgICAgICA8UGF5UGFsQnV0dG9uXG4gICAgICAgICAgICAgICAgYW1vdW50PXtwYXJzZUZsb2F0KHRvdGFsLnRvRml4ZWQoMikpfVxuICAgICAgICAgICAgICAgIHByb2R1Y3RJZD17cHJvZHVjdC5pZH1cbiAgICAgICAgICAgICAgICBvblN1Y2Nlc3M9e2hhbmRsZVBheW1lbnRTdWNjZXNzfVxuICAgICAgICAgICAgICAgIG9uRXJyb3I9e2hhbmRsZVBheW1lbnRFcnJvcn1cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7Lyog5a6J5YWo5o+Q56S6ICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDAgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBtYi0yXCI+XG4gICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00IG1yLTFcIiBmaWxsPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyMCAyMFwiPlxuICAgICAgICAgICAgICAgICAgPHBhdGggZmlsbFJ1bGU9XCJldmVub2RkXCIgZD1cIk01IDlWN2E1IDUgMCAwMTEwIDB2MmEyIDIgMCAwMTIgMnY1YTIgMiAwIDAxLTIgMkg1YTIgMiAwIDAxLTItMnYtNWEyIDIgMCAwMTItMnptOC0ydjJIN1Y3YTMgMyAwIDAxNiAwelwiIGNsaXBSdWxlPVwiZXZlbm9kZFwiIC8+XG4gICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAg5a6J5YWo5pSv5LuYXG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8cD7mgqjnmoTmlK/ku5jkv6Hmga/pgJrov4dQYXlQYWzlronlhajlpITnkIY8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIOi/lOWbnui0reeJqeaMiemSriAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNiBwdC00IGJvcmRlci10XCI+XG4gICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgaHJlZj1cIi9cIlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBiZy1ncmF5LTEwMCB0ZXh0LWdyYXktNzAwIHB4LTQgcHktMiByb3VuZGVkLWxnIGhvdmVyOmJnLWdyYXktMjAwIHRyYW5zaXRpb24tY29sb3JzIGZvbnQtbWVkaXVtIHRleHQtY2VudGVyIGJsb2NrXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIOe7p+e7rei0reeJqVxuICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIOaUr+S7mOWkhOeQhuS4reeahOmBrue9qSAqL31cbiAgICAgIHtwYXltZW50UHJvY2Vzc2luZyAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCBiZy1ibGFjayBiZy1vcGFjaXR5LTUwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHotNTBcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgcC02IHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC04IHctOCBib3JkZXItYi0yIGJvcmRlci1ibHVlLTYwMCBteC1hdXRvIG1iLTRcIj48L2Rpdj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS03MDBcIj7mraPlnKjlpITnkIbmlK/ku5guLi48L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cbiAgICA8L2Rpdj5cbiAgKTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQ2hlY2tvdXQoKSB7XG4gIHJldHVybiAoXG4gICAgPFN1c3BlbnNlIGZhbGxiYWNrPXtcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbWluLWgtNjRcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtOCB3LTggYm9yZGVyLWItMiBib3JkZXItYmx1ZS02MDBcIj48L2Rpdj5cbiAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibWwtMlwiPkxvYWRpbmcuLi48L3NwYW4+XG4gICAgICA8L2Rpdj5cbiAgICB9PlxuICAgICAgPENoZWNrb3V0Q29udGVudCAvPlxuICAgIDwvU3VzcGVuc2U+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiU3VzcGVuc2UiLCJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsInVzZVNlYXJjaFBhcmFtcyIsIkxpbmsiLCJQYXlQYWxCdXR0b24iLCJyb3VuZFRvVHdvRGVjaW1hbHMiLCJhbW91bnQiLCJNYXRoIiwicm91bmQiLCJDaGVja291dENvbnRlbnQiLCJzZWFyY2hQYXJhbXMiLCJwcm9kdWN0Iiwic2V0UHJvZHVjdCIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsInBheW1lbnRQcm9jZXNzaW5nIiwic2V0UGF5bWVudFByb2Nlc3NpbmciLCJwcm9kdWN0SWQiLCJnZXQiLCJwcmljZSIsIm5hbWUiLCJwcmljZU51bSIsInBhcnNlRmxvYXQiLCJpc05hTiIsImlkIiwiZGVjb2RlVVJJQ29tcG9uZW50IiwiZGVzY3JpcHRpb24iLCJoYW5kbGVQYXltZW50U3VjY2VzcyIsIm9yZGVyRGF0YSIsImNvbnNvbGUiLCJsb2ciLCJoYW5kbGVQYXltZW50RXJyb3IiLCJkaXYiLCJjbGFzc05hbWUiLCJzcGFuIiwic3ZnIiwiZmlsbCIsInZpZXdCb3giLCJwYXRoIiwiZmlsbFJ1bGUiLCJkIiwiY2xpcFJ1bGUiLCJocmVmIiwicCIsInN1YnRvdGFsIiwidGF4IiwidG90YWwiLCJoMSIsImgyIiwiaDMiLCJ0b0ZpeGVkIiwib25TdWNjZXNzIiwib25FcnJvciIsIkNoZWNrb3V0IiwiZmFsbGJhY2siXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/checkout/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/PayPalButton.tsx":
/*!*****************************************!*\
  !*** ./src/components/PayPalButton.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PayPalButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/script */ \"(ssr)/./node_modules/next/script.js\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_script__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction PayPalButton({ amount, productId, onSuccess, onError }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [scriptLoaded, setScriptLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [paypalLoaded, setPaypalLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [processing, setProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 步骤1: 创建订单请求\n    const createOrder = async ()=>{\n        try {\n            setProcessing(true);\n            setCurrentStep(\"步骤1: 创建订单请求\");\n            console.log(\"步骤1: 前端发起创建订单请求\");\n            // 步骤2: POST /v2/checkout/orders\n            setCurrentStep(\"步骤2: 调用PayPal API创建订单\");\n            const orderRequest = {\n                amount: parseFloat(amount.toFixed(2)),\n                currency: \"USD\",\n                description: `Product ${productId}`,\n                productId\n            };\n            const response = await fetch(`${process.env.BACKEND_API_URL || \"http://localhost:8080\"}/api/paypal/create-order`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(orderRequest)\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || \"Failed to create order\");\n            }\n            const data = await response.json();\n            // 步骤3&4: 返回order ID\n            setCurrentStep(\"步骤3&4: 返回Order ID\");\n            console.log(\"步骤3&4: 后端返回Order ID:\", data.id);\n            return data.id;\n        } catch (err) {\n            const errorMessage = err.message || \"Failed to create PayPal order. Please try again.\";\n            setError(errorMessage);\n            onError?.(errorMessage);\n            console.error(\"Error creating order:\", err);\n            throw err;\n        } finally{\n            setProcessing(false);\n        }\n    };\n    // 步骤5: 使用PayPal JS SDK发起支付 (由PayPal SDK自动处理)\n    // 步骤6: 支付完成重定向 (由PayPal SDK自动处理)\n    // 步骤7: 通知支付完成，步骤8: 捕获支付\n    const onApprove = async (data, actions)=>{\n        try {\n            setProcessing(true);\n            setCurrentStep(\"步骤7: 通知支付完成\");\n            console.log(\"步骤7: PayPal通知支付完成，Order ID:\", data.orderID);\n            // 步骤8: 捕获支付 (PATCH /orders/{id}/capture)\n            setCurrentStep(\"步骤8: 捕获支付\");\n            console.log(\"步骤8: 开始捕获支付\");\n            const captureRequest = {\n                orderId: data.orderID\n            };\n            const response = await fetch(`${process.env.BACKEND_API_URL || \"http://localhost:8080\"}/api/paypal/capture-order`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(captureRequest)\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                // 特殊处理ORDER_NOT_APPROVED错误\n                if (errorData.error && (errorData.error.includes(\"not approved\") || errorData.error.includes(\"ORDER_NOT_APPROVED\"))) {\n                    throw new Error(\"支付尚未完成批准，请确保您已在PayPal页面完成支付确认。\");\n                }\n                throw new Error(errorData.error || \"Failed to capture order\");\n            }\n            const orderData = await response.json();\n            console.log(\"支付捕获成功:\", orderData);\n            setCurrentStep(\"支付完成\");\n            // 调用成功回调\n            onSuccess?.(orderData);\n            // 重定向到成功页面\n            router.push(`/success?orderId=${data.orderID}`);\n        } catch (err) {\n            const errorMessage = err.message || \"Payment failed. Please try again.\";\n            setError(errorMessage);\n            onError?.(errorMessage);\n            console.error(\"Error capturing order:\", err);\n            // 如果是订单未批准错误，给用户更明确的提示\n            if (errorMessage.includes(\"not approved\") || errorMessage.includes(\"ORDER_NOT_APPROVED\")) {\n                setError(\"支付未完成：请确保您已在PayPal页面完成支付确认，然后重试。\");\n            }\n        } finally{\n            setProcessing(false);\n            setCurrentStep(\"\");\n        }\n    };\n    const initializePayPal = ()=>{\n        if (window.paypal) {\n            try {\n                const paypalConfig = {\n                    createOrder,\n                    onApprove,\n                    onError: (err)=>{\n                        const errorMessage = \"PayPal encountered an error. Please try again.\";\n                        setError(errorMessage);\n                        onError?.(errorMessage);\n                        console.error(\"PayPal error:\", err);\n                    },\n                    onCancel: ()=>{\n                        console.log(\"Payment cancelled\");\n                        setProcessing(false);\n                        setCurrentStep(\"\");\n                    },\n                    style: {\n                        layout: \"vertical\",\n                        color: \"blue\",\n                        shape: \"rect\",\n                        label: \"pay\"\n                    }\n                };\n                window.paypal.Buttons(paypalConfig).render(\"#paypal-button-container\");\n                setPaypalLoaded(true);\n                console.log(\"PayPal按钮初始化完成\");\n            } catch (error) {\n                console.error(\"Error rendering PayPal buttons:\", error);\n                setError(\"Failed to load PayPal. Please refresh and try again.\");\n            }\n        }\n    };\n    const handleScriptLoad = ()=>{\n        console.log(\"PayPal SDK加载完成\");\n        setScriptLoaded(true);\n        initializePayPal();\n    };\n    const clearError = ()=>{\n        setError(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"paypal-button-wrapper\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_script__WEBPACK_IMPORTED_MODULE_3___default()), {\n                src: `https://www.paypal.com/sdk/js?client-id=${\"AfNDRjiY0upudf0iL2OHXQv3FyMc3St9G6GotrnsqE9xNmGGvLBMYpcrs_cwEqaErT-2WZaQMfTsKGCT\"}&currency=USD&intent=capture&components=buttons`,\n                onLoad: handleScriptLoad,\n                strategy: \"lazyOnload\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/components/PayPalButton.tsx\",\n                lineNumber: 196,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/components/PayPalButton.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: clearError,\n                            className: \"text-red-700 hover:text-red-900 font-bold\",\n                            children: \"\\xd7\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/components/PayPalButton.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/components/PayPalButton.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/components/PayPalButton.tsx\",\n                lineNumber: 203,\n                columnNumber: 9\n            }, this),\n            processing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-700 mr-2\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/components/PayPalButton.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: currentStep || \"Processing payment...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/components/PayPalButton.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/components/PayPalButton.tsx\",\n                    lineNumber: 218,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/components/PayPalButton.tsx\",\n                lineNumber: 217,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"paypal-button-container\",\n                className: \"mt-4\",\n                children: !scriptLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center p-4 bg-gray-100 rounded\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-2\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/components/PayPalButton.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Loading PayPal...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/components/PayPalButton.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/components/PayPalButton.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/components/PayPalButton.tsx\",\n                lineNumber: 225,\n                columnNumber: 7\n            }, this),\n            scriptLoaded && !paypalLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 text-center text-gray-500\",\n                children: \"Initializing PayPal buttons...\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/components/PayPalButton.tsx\",\n                lineNumber: 237,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/components/PayPalButton.tsx\",\n        lineNumber: 195,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/PayPalButton.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"955ec95b3e6f\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGF5cGFsLWRlbW8tZnJvbnRlbmQvLi9zcmMvYXBwL2dsb2JhbHMuY3NzP2QyNjgiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI5NTVlYzk1YjNlNmZcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/checkout/page.tsx":
/*!***********************************!*\
  !*** ./src/app/checkout/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Projects/paypal-demo/frontend/src/app/checkout/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\nconst metadata = {\n    title: \"PayPal Demo - 支付集成演示\",\n    description: \"PayPal Orders API v2 + Vault 支付集成演示应用\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"min-h-screen bg-gray-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"bg-white shadow-sm border-b\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 py-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"PayPal Demo\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/layout.tsx\",\n                                        lineNumber: 21,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"flex space-x-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/\",\n                                                    className: \"text-blue-600 hover:text-blue-800 font-medium\",\n                                                    children: \"首页\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/layout.tsx\",\n                                                    lineNumber: 24,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/layout.tsx\",\n                                                lineNumber: 23,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/checkout\",\n                                                    className: \"text-blue-600 hover:text-blue-800 font-medium\",\n                                                    children: \"结账\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/layout.tsx\",\n                                                    lineNumber: 29,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/layout.tsx\",\n                                                lineNumber: 28,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/orders\",\n                                                    className: \"text-blue-600 hover:text-blue-800 font-medium\",\n                                                    children: \"订单管理\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/layout.tsx\",\n                                                    lineNumber: 34,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/layout.tsx\",\n                                                lineNumber: 33,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/vault\",\n                                                    className: \"text-blue-600 hover:text-blue-800 font-medium\",\n                                                    children: \"支付方式\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/layout.tsx\",\n                                                    lineNumber: 39,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/layout.tsx\",\n                                                lineNumber: 38,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/test-paypal\",\n                                                    className: \"text-orange-600 hover:text-orange-800 font-medium\",\n                                                    children: \"测试\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/layout.tsx\",\n                                                    lineNumber: 44,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/layout.tsx\",\n                                                lineNumber: 43,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/debug-order\",\n                                                    className: \"text-purple-600 hover:text-purple-800 font-medium\",\n                                                    children: \"调试\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/layout.tsx\",\n                                                    lineNumber: 49,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/layout.tsx\",\n                                                lineNumber: 48,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/layout.tsx\",\n                                        lineNumber: 22,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/layout.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/layout.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/layout.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto px-4 py-8\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/layout.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/layout.tsx\",\n                lineNumber: 17,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/layout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Projects/paypal-demo/frontend/src/app/layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcheckout%2Fpage&page=%2Fcheckout%2Fpage&appPaths=%2Fcheckout%2Fpage&pagePath=private-next-app-dir%2Fcheckout%2Fpage.tsx&appDir=%2FUsers%2Flinjinbin%2FProjects%2Fpaypal-demo%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Flinjinbin%2FProjects%2Fpaypal-demo%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();