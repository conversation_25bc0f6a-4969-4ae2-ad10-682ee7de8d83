'use client';

import { Suspense, useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import PayPalButton from '@/components/PayPalButton';
import { PayPalCaptureOrderResponse } from '@/types/paypal';

interface ProductInfo {
  id: string;
  name: string;
  price: number;
  description?: string;
}

function CheckoutContent() {
  const searchParams = useSearchParams();
  const [product, setProduct] = useState<ProductInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [paymentProcessing, setPaymentProcessing] = useState(false);

  useEffect(() => {
    // 从URL参数获取产品信息
    const productId = searchParams.get('productId');
    const price = searchParams.get('price');
    const name = searchParams.get('name');

    if (!productId || !price || !name) {
      setError('缺少必要的产品信息，请从商品页面重新选择。');
      setLoading(false);
      return;
    }

    const priceNum = parseFloat(price);
    if (isNaN(priceNum) || priceNum <= 0) {
      setError('无效的价格信息。');
      setLoading(false);
      return;
    }

    setProduct({
      id: productId,
      name: decodeURIComponent(name),
      price: priceNum,
      description: `商品ID: ${productId}`
    });
    setLoading(false);
  }, [searchParams]);

  const handlePaymentSuccess = (orderData: PayPalCaptureOrderResponse) => {
    console.log('支付成功:', orderData);
    setPaymentProcessing(false);
    // PayPalButton组件会自动重定向到成功页面
  };

  const handlePaymentError = (error: string) => {
    console.error('支付失败:', error);
    setError(error);
    setPaymentProcessing(false);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">加载中...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-2xl mx-auto">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <div className="flex items-center">
            <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            <span>{error}</span>
          </div>
        </div>
        <div className="text-center">
          <Link
            href="/"
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            返回首页
          </Link>
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="max-w-2xl mx-auto text-center">
        <p className="text-gray-600 mb-4">未找到产品信息</p>
        <Link
          href="/"
          className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium"
        >
          返回首页
        </Link>
      </div>
    );
  }

  // 计算费用
  const subtotal = product.price;
  const tax = subtotal * 0.08; // 8% 税率
  const total = subtotal + tax;

  return (
    <div className="max-w-4xl mx-auto">
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">结账</h1>
        <p className="text-gray-600">请确认您的订单信息并完成支付</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* 左侧：订单信息 */}
        <div className="lg:col-span-2">
          {/* 商品信息 */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">商品信息</h2>
            <div className="flex items-center space-x-4">
              <div className="w-20 h-20 bg-gray-200 rounded-lg flex items-center justify-center">
                <svg className="w-8 h-8 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-medium text-gray-900">{product.name}</h3>
                <p className="text-gray-600">{product.description}</p>
                <p className="text-lg font-semibold text-green-600 mt-2">${product.price.toFixed(2)}</p>
              </div>
            </div>
          </div>

          {/* 支付流程说明 */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-blue-800 mb-4">支付流程</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div className="flex items-center text-blue-700">
                <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center mr-3 text-xs font-bold">
                  1
                </div>
                点击PayPal支付按钮
              </div>
              <div className="flex items-center text-blue-700">
                <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center mr-3 text-xs font-bold">
                  2
                </div>
                在PayPal页面完成支付
              </div>
              <div className="flex items-center text-blue-700">
                <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center mr-3 text-xs font-bold">
                  3
                </div>
                系统自动处理订单
              </div>
              <div className="flex items-center text-blue-700">
                <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center mr-3 text-xs font-bold">
                  4
                </div>
                重定向到成功页面
              </div>
            </div>
          </div>
        </div>

        {/* 右侧：订单摘要和支付 */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-md p-6 sticky top-4">
            <h2 className="text-xl font-semibold mb-4">订单摘要</h2>

            {/* 费用明细 */}
            <div className="space-y-3 mb-6">
              <div className="flex justify-between">
                <span className="text-gray-600">商品小计</span>
                <span className="font-medium">${subtotal.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">税费 (8%)</span>
                <span className="font-medium">${tax.toFixed(2)}</span>
              </div>
              <div className="border-t pt-3">
                <div className="flex justify-between">
                  <span className="text-lg font-semibold">总计</span>
                  <span className="text-lg font-bold text-green-600">${total.toFixed(2)}</span>
                </div>
              </div>
            </div>

            {/* PayPal支付按钮 */}
            <div className="mb-4">
              <PayPalButton
                amount={total}
                productId={product.id}
                onSuccess={handlePaymentSuccess}
                onError={handlePaymentError}
              />
            </div>

            {/* 安全提示 */}
            <div className="text-xs text-gray-500 text-center">
              <div className="flex items-center justify-center mb-2">
                <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                </svg>
                安全支付
              </div>
              <p>您的支付信息通过PayPal安全处理</p>
            </div>

            {/* 返回购物按钮 */}
            <div className="mt-6 pt-4 border-t">
              <Link
                href="/"
                className="w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors font-medium text-center block"
              >
                继续购物
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* 支付处理中的遮罩 */}
      {paymentProcessing && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-700">正在处理支付...</p>
          </div>
        </div>
      )}
    </div>
  );
}

export default function Checkout() {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center min-h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">Loading...</span>
      </div>
    }>
      <CheckoutContent />
    </Suspense>
  );
}
