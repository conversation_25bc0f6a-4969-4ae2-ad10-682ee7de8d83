'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function TestPayPal() {
  const [testResult, setTestResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testBackendConnection = async () => {
    setLoading(true);
    setTestResult('');
    
    try {
      const response = await fetch(`${process.env.BACKEND_API_URL || 'http://localhost:8080'}/api/paypal/orders`);
      
      if (response.ok) {
        setTestResult('✅ 后端连接成功！');
      } else {
        setTestResult(`❌ 后端连接失败: ${response.status} ${response.statusText}`);
      }
    } catch (error: any) {
      setTestResult(`❌ 后端连接错误: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testCreateOrder = async () => {
    setLoading(true);
    setTestResult('');
    
    try {
      const response = await fetch(`${process.env.BACKEND_API_URL || 'http://localhost:8080'}/api/paypal/create-order`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: 1.00,
          currency: 'USD',
          description: 'Test Order',
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setTestResult(`✅ 订单创建成功！Order ID: ${data.id}`);
      } else {
        const errorData = await response.json();
        setTestResult(`❌ 订单创建失败: ${errorData.error}`);
      }
    } catch (error: any) {
      setTestResult(`❌ 订单创建错误: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto">
      <div className="mb-6">
        <Link href="/" className="text-blue-600 hover:text-blue-800 flex items-center">
          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
          </svg>
          返回首页
        </Link>
      </div>

      <h1 className="text-3xl font-bold text-gray-900 mb-8">PayPal 配置测试</h1>

      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">环境配置检查</h2>
        
        <div className="space-y-4">
          <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
            <span>PayPal Client ID:</span>
            <span className="font-mono text-sm">
              {process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID ? 
                `${process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID.substring(0, 10)}...` : 
                '❌ 未配置'
              }
            </span>
          </div>
          
          <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
            <span>后端API地址:</span>
            <span className="font-mono text-sm">
              {process.env.BACKEND_API_URL || 'http://localhost:8080'}
            </span>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">连接测试</h2>
        
        <div className="space-y-4">
          <button
            onClick={testBackendConnection}
            disabled={loading}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 disabled:bg-gray-400"
          >
            {loading ? '测试中...' : '测试后端连接'}
          </button>
          
          <button
            onClick={testCreateOrder}
            disabled={loading}
            className="w-full bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700 disabled:bg-gray-400"
          >
            {loading ? '测试中...' : '测试创建订单'}
          </button>
        </div>
        
        {testResult && (
          <div className="mt-4 p-3 bg-gray-50 rounded">
            <pre className="text-sm">{testResult}</pre>
          </div>
        )}
      </div>

      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-yellow-800 mb-3">故障排除指南</h3>
        <div className="space-y-2 text-sm text-yellow-700">
          <div className="flex items-start">
            <span className="font-semibold mr-2">1.</span>
            <span>确保后端服务正在运行 (http://localhost:8080)</span>
          </div>
          <div className="flex items-start">
            <span className="font-semibold mr-2">2.</span>
            <span>检查 PayPal 凭据是否正确配置在后端 application.properties 中</span>
          </div>
          <div className="flex items-start">
            <span className="font-semibold mr-2">3.</span>
            <span>确保使用的是 sandbox 环境的凭据进行测试</span>
          </div>
          <div className="flex items-start">
            <span className="font-semibold mr-2">4.</span>
            <span>检查网络连接和防火墙设置</span>
          </div>
        </div>
      </div>

      <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-blue-800 mb-3">ORDER_NOT_APPROVED 错误解决方案</h3>
        <div className="space-y-2 text-sm text-blue-700">
          <div className="flex items-start">
            <span className="font-semibold mr-2">•</span>
            <span>这个错误表示用户还没有在PayPal页面完成支付批准</span>
          </div>
          <div className="flex items-start">
            <span className="font-semibold mr-2">•</span>
            <span>确保用户完成了PayPal支付流程中的所有步骤</span>
          </div>
          <div className="flex items-start">
            <span className="font-semibold mr-2">•</span>
            <span>只有在 onApprove 回调被触发后才能捕获支付</span>
          </div>
          <div className="flex items-start">
            <span className="font-semibold mr-2">•</span>
            <span>检查订单状态是否为 "APPROVED" 再进行捕获</span>
          </div>
        </div>
      </div>
    </div>
  );
}
