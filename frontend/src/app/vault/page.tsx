'use client';

import Link from 'next/link';

export default function VaultPage() {
  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-6">
        <Link href="/" className="text-blue-600 hover:text-blue-800 flex items-center">
          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
          </svg>
          返回首页
        </Link>
      </div>

      <div className="flex items-center justify-between mb-8">
        <h1 className="text-3xl font-bold text-gray-900">支付方式管理</h1>
      </div>

      <div className="text-center py-12">
        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg className="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
            <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z" />
            <path fillRule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" clipRule="evenodd" />
          </svg>
        </div>
        <h2 className="text-xl font-semibold text-gray-900 mb-2">支付方式管理功能已移除</h2>
        <p className="text-gray-600 mb-6">
          此版本专注于基本的PayPal支付和退款功能。
        </p>
        <Link
          href="/"
          className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium"
        >
          返回首页
        </Link>
      </div>

      {/* 功能说明 */}
      <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-blue-800 mb-3">当前支持的功能</h3>
        <div className="space-y-2 text-sm text-blue-700">
          <div className="flex items-start">
            <span className="font-semibold mr-2">✅</span>
            <span>创建PayPal订单</span>
          </div>
          <div className="flex items-start">
            <span className="font-semibold mr-2">✅</span>
            <span>捕获PayPal支付</span>
          </div>
          <div className="flex items-start">
            <span className="font-semibold mr-2">✅</span>
            <span>处理PayPal退款</span>
          </div>
          <div className="flex items-start">
            <span className="font-semibold mr-2">✅</span>
            <span>查看订单详情</span>
          </div>
          <div className="flex items-start">
            <span className="font-semibold mr-2">✅</span>
            <span>查看退款记录</span>
          </div>
        </div>
      </div>
    </div>
  );
}
