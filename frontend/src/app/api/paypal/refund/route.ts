import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    const { orderId, amount } = await request.json();

    if (!orderId) {
      return NextResponse.json(
        { error: 'Order ID is required' },
        { status: 400 }
      );
    }

    if (!amount || isNaN(amount) || amount <= 0) {
      return NextResponse.json(
        { error: 'Valid refund amount is required' },
        { status: 400 }
      );
    }

    // Get access token
    const accessToken = await getPayPalAccessToken();

    // Get capture ID from order
    const orderDetails = await getPayPalOrderDetails(accessToken, orderId);
    const captureId = getCaptureIdFromOrder(orderDetails);

    if (!captureId) {
      return NextResponse.json(
        { error: 'No capture ID found for this order' },
        { status: 400 }
      );
    }

    // Process refund
    const refundData = await processPayPalRefund(accessToken, captureId, amount);

    // In a real application, you would update the order in your database here
    // updateOrderRefundStatus(orderId, refundData);

    return NextResponse.json({
      success: true,
      refundId: refundData.id,
      status: refundData.status,
    });
  } catch (error: any) {
    console.error('Error processing PayPal refund:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to process refund' },
      { status: 500 }
    );
  }
}

async function getPayPalAccessToken() {
  const clientId = process.env.PAYPAL_CLIENT_ID;
  const clientSecret = process.env.PAYPAL_CLIENT_SECRET;
  const paypalUrl = process.env.PAYPAL_API_URL || 'https://api-m.sandbox.paypal.com';

  if (!clientId || !clientSecret) {
    throw new Error('PayPal credentials are not configured');
  }

  const auth = Buffer.from(`${clientId}:${clientSecret}`).toString('base64');

  const response = await fetch(`${paypalUrl}/v1/oauth2/token`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Authorization': `Basic ${auth}`,
    },
    body: 'grant_type=client_credentials',
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(`Failed to get PayPal access token: ${errorData.error_description}`);
  }

  const data = await response.json();
  return data.access_token;
}

async function getPayPalOrderDetails(accessToken: string, orderId: string) {
  const paypalUrl = process.env.PAYPAL_API_URL || 'https://api-m.sandbox.paypal.com';

  const response = await fetch(`${paypalUrl}/v2/checkout/orders/${orderId}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`,
    },
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(`Failed to get PayPal order details: ${errorData.message}`);
  }

  return response.json();
}

function getCaptureIdFromOrder(orderDetails: any) {
  try {
    // Navigate through the order structure to find the capture ID
    const captureId = orderDetails.purchase_units[0]?.payments?.captures[0]?.id;
    return captureId;
  } catch (error) {
    console.error('Error extracting capture ID:', error);
    return null;
  }
}

async function processPayPalRefund(accessToken: string, captureId: string, amount: number) {
  const paypalUrl = process.env.PAYPAL_API_URL || 'https://api-m.sandbox.paypal.com';

  const response = await fetch(`${paypalUrl}/v2/payments/captures/${captureId}/refund`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`,
    },
    body: JSON.stringify({
      amount: {
        value: amount.toString(),
        currency_code: 'USD',
      },
    }),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(`Failed to process PayPal refund: ${errorData.message}`);
  }

  return response.json();
}
