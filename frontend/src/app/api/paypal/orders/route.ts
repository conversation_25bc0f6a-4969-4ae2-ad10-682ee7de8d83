import { NextResponse } from 'next/server';

// In a real application, you would fetch this data from your database
// This is just a mock implementation for the demo
const mockOrders = [
  {
    id: 'ORDER123456789',
    amount: '19.99',
    status: 'COMPLETED',
    date: '2023-05-15',
  },
  {
    id: 'ORDER987654321',
    amount: '29.99',
    status: 'COMPLETED',
    date: '2023-05-10',
    refundStatus: 'Partially Refunded',
  },
  {
    id: 'ORDER456789123',
    amount: '39.99',
    status: 'COMPLETED',
    date: '2023-05-05',
  },
];

export async function GET() {
  try {
    // In a real application, you would fetch orders from your database
    // For this demo, we'll just return mock data
    return NextResponse.json({ orders: mockOrders });
  } catch (error: any) {
    console.error('Error fetching orders:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch orders' },
      { status: 500 }
    );
  }
}
