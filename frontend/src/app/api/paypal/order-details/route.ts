import { NextResponse } from 'next/server';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const orderId = searchParams.get('orderId');

    if (!orderId) {
      return NextResponse.json(
        { error: 'Order ID is required' },
        { status: 400 }
      );
    }

    // Get access token
    const accessToken = await getPayPalAccessToken();

    // Get order details
    const orderDetails = await getPayPalOrderDetails(accessToken, orderId);

    return NextResponse.json(orderDetails);
  } catch (error: any) {
    console.error('Error getting PayPal order details:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to get order details' },
      { status: 500 }
    );
  }
}

async function getPayPalAccessToken() {
  const clientId = process.env.PAYPAL_CLIENT_ID;
  const clientSecret = process.env.PAYPAL_CLIENT_SECRET;
  const paypalUrl = process.env.PAYPAL_API_URL || 'https://api-m.sandbox.paypal.com';

  if (!clientId || !clientSecret) {
    throw new Error('PayPal credentials are not configured');
  }

  const auth = Buffer.from(`${clientId}:${clientSecret}`).toString('base64');

  const response = await fetch(`${paypalUrl}/v1/oauth2/token`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Authorization': `Basic ${auth}`,
    },
    body: 'grant_type=client_credentials',
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(`Failed to get PayPal access token: ${errorData.error_description}`);
  }

  const data = await response.json();
  return data.access_token;
}

async function getPayPalOrderDetails(accessToken: string, orderId: string) {
  const paypalUrl = process.env.PAYPAL_API_URL || 'https://api-m.sandbox.paypal.com';

  const response = await fetch(`${paypalUrl}/v2/checkout/orders/${orderId}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`,
    },
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(`Failed to get PayPal order details: ${errorData.message}`);
  }

  return response.json();
}
