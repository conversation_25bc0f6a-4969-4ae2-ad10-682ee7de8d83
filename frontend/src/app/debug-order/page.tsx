'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function DebugOrder() {
  const [orderId, setOrderId] = useState('');
  const [orderData, setOrderData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchOrderDetails = async () => {
    if (!orderId.trim()) {
      setError('请输入订单ID');
      return;
    }

    setLoading(true);
    setError(null);
    setOrderData(null);

    try {
      const response = await fetch(`${process.env.BACKEND_API_URL || 'http://localhost:8080'}/api/paypal/order-details/${orderId.trim()}`);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      setOrderData(data);
    } catch (err: any) {
      setError(err.message || '获取订单详情失败');
      console.error('Error fetching order details:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    fetchOrderDetails();
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-6">
        <Link href="/" className="text-blue-600 hover:text-blue-800 flex items-center">
          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
          </svg>
          返回首页
        </Link>
      </div>

      <h1 className="text-3xl font-bold text-gray-900 mb-8">订单数据调试</h1>

      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">查询订单详情</h2>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="orderId" className="block text-sm font-medium text-gray-700 mb-2">
              PayPal 订单 ID
            </label>
            <input
              type="text"
              id="orderId"
              value={orderId}
              onChange={(e) => setOrderId(e.target.value)}
              placeholder="例如: 02K57399995236631"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <button
            type="submit"
            disabled={loading}
            className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 disabled:bg-gray-400"
          >
            {loading ? '查询中...' : '查询订单'}
          </button>
        </form>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          <h3 className="font-semibold">错误</h3>
          <p>{error}</p>
        </div>
      )}

      {orderData && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">订单数据</h2>
          
          <div className="mb-6">
            <h3 className="text-lg font-medium mb-2">基本信息</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">订单ID:</span> {orderData.id}
              </div>
              <div>
                <span className="font-medium">状态:</span> {orderData.status}
              </div>
              <div>
                <span className="font-medium">创建时间:</span> {orderData.create_time}
              </div>
              <div>
                <span className="font-medium">更新时间:</span> {orderData.update_time}
              </div>
            </div>
          </div>

          <div className="mb-6">
            <h3 className="text-lg font-medium mb-2">Purchase Units 分析</h3>
            {orderData.purchase_units && orderData.purchase_units.length > 0 ? (
              <div className="space-y-4">
                {orderData.purchase_units.map((unit: any, index: number) => (
                  <div key={index} className="border border-gray-200 rounded p-4">
                    <h4 className="font-medium mb-2">Purchase Unit {index + 1}</h4>
                    
                    {unit.amount ? (
                      <div className="mb-2">
                        <span className="font-medium text-green-600">✅ 金额信息:</span>
                        <div className="ml-4">
                          <div>值: {unit.amount.value}</div>
                          <div>货币: {unit.amount.currency_code}</div>
                        </div>
                      </div>
                    ) : (
                      <div className="mb-2">
                        <span className="font-medium text-red-600">❌ 缺少金额信息</span>
                      </div>
                    )}

                    {unit.payments && unit.payments.captures ? (
                      <div>
                        <span className="font-medium text-green-600">✅ 捕获信息:</span>
                        <div className="ml-4">
                          {unit.payments.captures.map((capture: any, captureIndex: number) => (
                            <div key={captureIndex} className="border-l-2 border-blue-200 pl-2 mt-2">
                              <div>捕获ID: {capture.id}</div>
                              <div>状态: {capture.status}</div>
                              {capture.amount && (
                                <div>金额: {capture.amount.value} {capture.amount.currency_code}</div>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    ) : (
                      <div>
                        <span className="font-medium text-yellow-600">⚠️ 无捕获信息</span>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-red-600">❌ Purchase Units 为空或不存在</div>
            )}
          </div>

          <div className="mb-6">
            <h3 className="text-lg font-medium mb-2">完整 JSON 数据</h3>
            <pre className="bg-gray-100 p-4 rounded text-xs overflow-auto max-h-96">
              {JSON.stringify(orderData, null, 2)}
            </pre>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded p-4">
            <h3 className="text-lg font-medium text-blue-800 mb-2">数据分析结果</h3>
            <div className="text-sm text-blue-700 space-y-1">
              {orderData.purchase_units && orderData.purchase_units[0] && orderData.purchase_units[0].amount ? (
                <div>✅ 可以从 purchase_units[0].amount 获取金额</div>
              ) : (
                <div>❌ purchase_units[0].amount 不可用</div>
              )}
              
              {orderData.purchase_units && 
               orderData.purchase_units[0] && 
               orderData.purchase_units[0].payments && 
               orderData.purchase_units[0].payments.captures && 
               orderData.purchase_units[0].payments.captures[0] && 
               orderData.purchase_units[0].payments.captures[0].amount ? (
                <div>✅ 可以从 captures[0].amount 获取金额</div>
              ) : (
                <div>❌ captures[0].amount 不可用</div>
              )}
              
              <div className="mt-2 font-medium">
                建议: {orderData.purchase_units && orderData.purchase_units[0] && orderData.purchase_units[0].amount 
                  ? '使用 purchase_units[0].amount' 
                  : '需要从数据库获取金额信息或修复 PayPal API 调用'}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
