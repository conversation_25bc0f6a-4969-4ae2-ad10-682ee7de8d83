// 税费计算测试
const roundToTwoDecimals = (amount) => {
  return Math.round(amount * 100) / 100;
};

// 测试用例
const testCases = [
  { price: 19.99, expectedTax: 1.60, expectedTotal: 21.59 },
  { price: 29.99, expectedTax: 2.40, expectedTotal: 32.39 },
  { price: 39.99, expectedTax: 3.20, expectedTotal: 43.19 },
  { price: 10.00, expectedTax: 0.80, expectedTotal: 10.80 },
  { price: 100.00, expectedTax: 8.00, expectedTotal: 108.00 },
];

console.log('税费计算测试结果:');
console.log('================');

testCases.forEach((testCase, index) => {
  const subtotal = roundToTwoDecimals(testCase.price);
  const tax = roundToTwoDecimals(subtotal * 0.08);
  const total = roundToTwoDecimals(subtotal + tax);
  
  const taxMatch = tax === testCase.expectedTax;
  const totalMatch = total === testCase.expectedTotal;
  
  console.log(`测试 ${index + 1}:`);
  console.log(`  商品价格: $${testCase.price}`);
  console.log(`  计算税费: $${tax} (期望: $${testCase.expectedTax}) ${taxMatch ? '✓' : '✗'}`);
  console.log(`  计算总计: $${total} (期望: $${testCase.expectedTotal}) ${totalMatch ? '✓' : '✗'}`);
  console.log(`  结果: ${taxMatch && totalMatch ? '通过' : '失败'}`);
  console.log('');
});
