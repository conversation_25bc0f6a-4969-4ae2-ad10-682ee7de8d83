// PayPal 工具函数

export interface PayPalOrderDetails {
  id: string;
  status: string;
  purchase_units?: Array<{
    amount?: {
      value: string;
      currency_code: string;
    };
    payments?: {
      captures?: Array<{
        id: string;
        status: string;
        amount?: {
          value: string;
          currency_code: string;
        };
      }>;
    };
  }>;
  payer?: any;
  create_time?: string;
  update_time?: string;
}

export interface AmountInfo {
  value: string;
  currency: string;
}

/**
 * 安全地从PayPal订单详情中获取金额信息
 * @param orderDetails PayPal订单详情
 * @returns 金额信息或null
 */
export function getOrderAmount(orderDetails: PayPalOrderDetails): AmountInfo | null {
  if (!orderDetails || !orderDetails.purchase_units || !orderDetails.purchase_units[0]) {
    return null;
  }

  const purchaseUnit = orderDetails.purchase_units[0];

  // 首先尝试从 purchase_units.amount 获取
  if (purchaseUnit.amount && purchaseUnit.amount.value) {
    return {
      value: purchaseUnit.amount.value,
      currency: purchaseUnit.amount.currency_code || 'USD',
    };
  }

  // 如果没有，尝试从 captures 获取
  if (purchaseUnit.payments && 
      purchaseUnit.payments.captures && 
      purchaseUnit.payments.captures[0] && 
      purchaseUnit.payments.captures[0].amount) {
    const capture = purchaseUnit.payments.captures[0];
    return {
      value: capture.amount.value,
      currency: capture.amount.currency_code || 'USD',
    };
  }

  return null;
}

/**
 * 格式化金额显示
 * @param amount 金额信息
 * @returns 格式化的金额字符串
 */
export function formatAmount(amount: AmountInfo): string {
  return `$${amount.value} ${amount.currency}`;
}

/**
 * 检查订单是否可以退款
 * @param orderDetails PayPal订单详情
 * @returns 是否可以退款
 */
export function canRefundOrder(orderDetails: PayPalOrderDetails): boolean {
  return orderDetails.status === 'COMPLETED';
}

/**
 * 获取订单的捕获ID（用于退款）
 * @param orderDetails PayPal订单详情
 * @returns 捕获ID或null
 */
export function getCaptureId(orderDetails: PayPalOrderDetails): string | null {
  if (!orderDetails || !orderDetails.purchase_units || !orderDetails.purchase_units[0]) {
    return null;
  }

  const purchaseUnit = orderDetails.purchase_units[0];
  
  if (purchaseUnit.payments && 
      purchaseUnit.payments.captures && 
      purchaseUnit.payments.captures[0]) {
    return purchaseUnit.payments.captures[0].id;
  }

  return null;
}

/**
 * 安全地获取支付者信息
 * @param orderDetails PayPal订单详情
 * @returns 支付者信息
 */
export function getPayerInfo(orderDetails: PayPalOrderDetails): {
  name?: string;
  email?: string;
  payerId?: string;
} {
  if (!orderDetails || !orderDetails.payer) {
    return {};
  }

  const payer = orderDetails.payer;
  return {
    name: payer.name?.given_name && payer.name?.surname 
      ? `${payer.name.given_name} ${payer.name.surname}` 
      : payer.name?.full_name,
    email: payer.email_address,
    payerId: payer.payer_id,
  };
}

/**
 * 格式化日期时间
 * @param dateString ISO日期字符串
 * @returns 格式化的日期时间字符串
 */
export function formatDateTime(dateString: string): string {
  try {
    return new Date(dateString).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  } catch (error) {
    return dateString;
  }
}

/**
 * 获取订单状态的中文显示
 * @param status PayPal订单状态
 * @returns 中文状态
 */
export function getOrderStatusText(status: string): string {
  const statusMap: { [key: string]: string } = {
    'CREATED': '已创建',
    'SAVED': '已保存',
    'APPROVED': '已批准',
    'VOIDED': '已作废',
    'COMPLETED': '已完成',
    'PAYER_ACTION_REQUIRED': '需要支付者操作',
  };

  return statusMap[status] || status;
}

/**
 * 获取捕获状态的中文显示
 * @param status PayPal捕获状态
 * @returns 中文状态
 */
export function getCaptureStatusText(status: string): string {
  const statusMap: { [key: string]: string } = {
    'COMPLETED': '已完成',
    'DECLINED': '已拒绝',
    'PARTIALLY_REFUNDED': '部分退款',
    'PENDING': '待处理',
    'REFUNDED': '已退款',
  };

  return statusMap[status] || status;
}
