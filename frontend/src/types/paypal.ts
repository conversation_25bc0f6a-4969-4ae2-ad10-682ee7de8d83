// PayPal相关类型定义

export interface PayPalOrder {
  id: string;
  status: string;
  amount: number;
  currency: string;
  createdAt: string;
  updatedAt?: string;
}

export interface PayPalCreateOrderRequest {
  amount: number;
  currency: string;
  description: string;
  productId?: string | number;
}

export interface PayPalCreateOrderResponse {
  id: string;
  status: string;
  links?: Array<{
    href: string;
    rel: string;
    method: string;
  }>;
}

export interface PayPalCaptureOrderRequest {
  orderId: string;
}

export interface PayPalCaptureOrderResponse {
  id: string;
  status: string;
  purchase_units?: Array<{
    amount: {
      currency_code: string;
      value: string;
    };
    payments?: {
      captures?: Array<{
        id: string;
        status: string;
        amount: {
          currency_code: string;
          value: string;
        };
      }>;
    };
  }>;
}



// PayPal SDK相关类型
declare global {
  interface Window {
    paypal: {
      Buttons: (config: PayPalButtonsConfig) => {
        render: (selector: string) => Promise<void>;
      };
    };
  }
}

export interface PayPalButtonsConfig {
  createOrder: () => Promise<string>;
  onApprove: (data: PayPalApprovalData, actions: PayPalActions) => Promise<void>;
  onError?: (err: any) => void;
  onCancel?: () => void;
  style?: {
    layout?: string;
    color?: string;
    shape?: string;
    label?: string;
  };
}

export interface PayPalApprovalData {
  orderID: string;
  payerID?: string;
  paymentID?: string;
  billingToken?: string;
  facilitatorAccessToken?: string;
}

export interface PayPalActions {
  order: {
    get: () => Promise<any>;
    capture: () => Promise<any>;
  };
  payment?: {
    get: () => Promise<any>;
  };
}
