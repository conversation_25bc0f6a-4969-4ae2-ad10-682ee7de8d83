# PayPal 支付和退款系统

## 概述

这是一个简化的PayPal支付系统，专注于基本的支付和退款功能。

## 功能特性

### ✅ 支持的功能
- **创建PayPal订单** - 使用PayPal Orders API v2
- **捕获支付** - 完成支付流程
- **处理退款** - 支持全额和部分退款
- **订单管理** - 查看订单详情和状态
- **退款记录** - 查看退款历史

### ❌ 已移除的功能
- PayPal Vault支付方式存储
- 已保存支付方式管理
- 自动支付功能

## 技术栈

### 后端
- **Java 8**
- **Spring Boot 2.5.10**
- **PayPal Java SDK**
- **H2 数据库** (内存数据库)
- **JPA/Hibernate**

### 前端
- **Next.js 14**
- **React 18**
- **TypeScript**
- **Tailwind CSS**
- **PayPal JavaScript SDK**

## 快速开始

### 1. 环境配置

#### PayPal沙盒配置
1. 访问 [PayPal Developer](https://developer.paypal.com/)
2. 创建沙盒应用
3. 获取Client ID和Client Secret

#### 后端配置
更新 `backend/src/main/resources/application.properties`:
```properties
paypal.client.id=你的PayPal_Client_ID
paypal.client.secret=你的PayPal_Client_Secret
paypal.mode=sandbox
```

#### 前端配置
更新 `frontend/.env.local`:
```
NEXT_PUBLIC_PAYPAL_CLIENT_ID=你的PayPal_Client_ID
BACKEND_API_URL=http://localhost:8080
```

### 2. 启动应用

#### 启动后端
```bash
cd backend
./mvnw spring-boot:run
```

#### 启动前端
```bash
cd frontend
npm install
npm run dev
```

### 3. 访问应用
- 前端: http://localhost:3000
- 后端API: http://localhost:8080

## API端点

### 支付相关
- `POST /api/paypal/create-order` - 创建订单
- `POST /api/paypal/capture-order` - 捕获支付
- `GET /api/paypal/order-details/{orderId}` - 获取订单详情

### 退款相关
- `POST /api/paypal/refund` - 创建退款
- `GET /api/paypal/refunds` - 获取退款列表

### 查询相关
- `GET /api/paypal/orders` - 获取订单列表

## 支付流程

### 标准支付流程
1. **用户选择商品** → 点击"立即购买"
2. **创建订单** → 调用 `/create-order` API
3. **PayPal支付** → 用户在PayPal页面完成支付
4. **捕获支付** → 调用 `/capture-order` API
5. **支付完成** → 重定向到成功页面

### 退款流程
1. **访问订单页面** → 查看已完成的订单
2. **发起退款** → 点击"退款"按钮
3. **处理退款** → 调用 `/refund` API
4. **退款完成** → 更新订单状态

## 页面说明

### 主要页面
- **首页** (`/`) - 产品展示和购买入口
- **结账页面** (`/checkout`) - PayPal支付流程
- **成功页面** (`/success`) - 支付成功确认
- **订单页面** (`/orders`) - 订单管理和退款
- **调试页面** (`/debug-order`) - 开发调试工具

### 已移除页面
- `/vault` - 支付方式管理（已简化为信息页面）
- `/vault/setup` - 支付方式设置（已删除）
- `/test-vault` - Vault支付测试（已删除）

## 数据库结构

### 主要表
- **orders** - 订单信息
- **payment_transactions** - 支付交易记录
- **refunds** - 退款记录
- **vaulted_payment_methods** - 已保存支付方式（保留但不使用）

## 开发说明

### 添加新功能
1. 后端：在 `PayPalService` 中添加业务逻辑
2. 控制器：在 `PayPalController` 中添加API端点
3. 前端：创建相应的组件和页面

### 调试工具
- **调试页面** - `/debug-order` 可以查看订单详细信息
- **H2控制台** - http://localhost:8080/h2-console (开发环境)
- **浏览器开发工具** - 查看网络请求和控制台日志

## 常见问题

### 支付失败
1. 检查PayPal配置是否正确
2. 确认沙盒账户有足够余额
3. 查看浏览器控制台错误信息

### 订单状态异常
1. 检查PayPal webhook配置
2. 确认网络连接正常
3. 查看后端日志

### 前端显示问题
1. 确认环境变量配置正确
2. 检查PayPal SDK加载状态
3. 验证API端点可访问性

## 生产部署

### 环境切换
1. 将 `paypal.mode` 改为 `live`
2. 使用生产环境的Client ID和Secret
3. 更新前端环境变量

### 安全考虑
1. 保护敏感配置信息
2. 启用HTTPS
3. 实现用户认证和授权
4. 添加请求限制和监控

## 支持

如有问题，请检查：
1. PayPal开发者文档
2. 项目日志文件
3. 网络连接状态
4. 配置文件正确性
