# PayPal Demo Application

This is a demo application showcasing PayPal integration using Orders API (v2) and Vault functionality. The application demonstrates both payment and refund flows.

## Project Structure

- **Frontend**: Next.js application
- **Backend**: Spring Boot 2.5.10 application (Java 8 compatible)

## Prerequisites

- Node.js (v14+)
- Java 8+
- Maven
- PayPal Developer Account

## PayPal Setup

1. Create a PayPal Developer Account at [developer.paypal.com](https://developer.paypal.com)
2. Create a new app in the PayPal Developer Dashboard
3. Get your Client ID and Secret Key
4. Update the credentials in:
   - `frontend/.env.local`
   - `backend/src/main/resources/application.properties`

## Running the Application

### Backend (Spring Boot)

1. Navigate to the backend directory:
   ```
   cd backend
   ```

2. Build the application:
   ```
   mvn clean install
   ```

3. Run the application:
   ```
   mvn spring-boot:run
   ```

The backend will start on http://localhost:8080

### Frontend (Next.js)

1. Navigate to the frontend directory:
   ```
   cd frontend
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Update the environment variables in `.env.local`:
   ```
   NEXT_PUBLIC_PAYPAL_CLIENT_ID=your_paypal_client_id
   NEXT_PUBLIC_APP_URL=http://localhost:3000
   BACKEND_API_URL=http://localhost:8080
   ```

4. Run the development server:
   ```
   npm run dev
   ```

The frontend will start on http://localhost:3000

## Features

### Payment Flow (根据流程图实现)

1. **步骤1**: 前端创建订单请求
2. **步骤2**: 后端调用 PayPal API `POST /v2/checkout/orders`
3. **步骤3&4**: 返回 Order ID 给前端
4. **步骤5**: 使用 PayPal JS SDK 发起支付
5. **步骤6**: 支付完成重定向
6. **步骤7**: 通知支付完成
7. **步骤8**: 后端捕获支付 `PATCH /orders/{id}/capture`

### Vault 支付方式存储流程

1. **用户首次支付**: 完成 PayPal 支付流程
2. **通过Vault存储支付方式**: 自动保存支付信息到 PayPal Vault
3. **获得payment_token**: 用于后续快速支付
4. **后续支付直接使用token**: 无需重新输入支付信息

### Refund Flow

1. View your orders on the orders page
2. Click "Refund" next to an order
3. Enter the refund amount
4. Submit the refund request

### 页面功能

- **首页**: 商品展示和支付流程说明
- **结账页**: 支持新支付方式和已保存支付方式
- **成功页**: 显示支付结果和订单详情
- **订单管理**: 查看所有订单和处理退款
- **支付方式管理**: 管理已保存的 Vault 支付方式

## API Endpoints

### Frontend API Routes

- `POST /api/paypal/create-order` - Create a PayPal order
- `POST /api/paypal/capture-order` - Capture a PayPal order
- `GET /api/paypal/order-details` - Get order details
- `GET /api/paypal/orders` - Get all orders
- `POST /api/paypal/refund` - Process a refund

### Backend API Endpoints

- `POST /api/paypal/create-order` - Create a PayPal order
- `POST /api/paypal/capture-order` - Capture a PayPal order
- `GET /api/paypal/order-details/{orderId}` - Get order details
- `POST /api/paypal/refund` - Process a refund
- `POST /api/paypal/vault-payment-method` - Vault a payment method
- `GET /api/paypal/payment-methods/{customerId}` - Get vaulted payment methods
- `GET /api/paypal/orders` - Get all orders

## PayPal Integration Details

This application uses:

1. **Orders API v2** - For creating and capturing payments
2. **Vault** - For storing payment methods for future use
3. **Refund API** - For processing refunds

## Java 8 & Spring Boot 2.5.10 Compatibility

The backend has been specifically configured for Java 8 and Spring Boot 2.5.10:

- **Java Version**: 1.8 (Java 8)
- **Spring Boot Version**: 2.5.10
- **JPA**: Uses `javax.persistence.*` instead of `jakarta.persistence.*`
- **Dependencies**: Compatible with Java 8 ecosystem

## Security Considerations

- This is a demo application and does not implement all security best practices
- In a production environment, you should:
  - Use HTTPS
  - Implement proper authentication and authorization
  - Store sensitive data securely
  - Validate all inputs
  - Implement proper error handling