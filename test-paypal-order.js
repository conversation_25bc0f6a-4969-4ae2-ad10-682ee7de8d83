// 测试PayPal订单创建
const fetch = require('node-fetch');

async function testCreateOrder() {
  try {
    console.log('测试PayPal订单创建...');
    
    const orderData = {
      amount: 21.59,
      currency: 'USD',
      description: 'Test Product',
      productId: 'test-1'
    };
    
    console.log('发送订单数据:', orderData);
    
    const response = await fetch('http://localhost:8080/api/paypal/create-order', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(orderData)
    });
    
    if (!response.ok) {
      const errorData = await response.text();
      console.error('请求失败:', response.status, errorData);
      return;
    }
    
    const result = await response.json();
    console.log('订单创建成功:', result);
    
  } catch (error) {
    console.error('测试失败:', error.message);
  }
}

// 运行测试
testCreateOrder();
