package com.example.paypal.service;

import com.example.paypal.model.Order;
import com.example.paypal.model.PaymentTransaction;
import com.example.paypal.model.Refund;
import com.example.paypal.model.VaultedPaymentMethod;
import com.example.paypal.repository.OrderRepository;
import com.example.paypal.repository.PaymentTransactionRepository;
import com.example.paypal.repository.RefundRepository;
import com.example.paypal.repository.VaultedPaymentMethodRepository;
import com.paypal.core.PayPalHttpClient;
import com.paypal.http.HttpResponse;
import com.paypal.orders.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class PayPalServiceImpl implements PayPalService {

    private final PayPalHttpClient payPalHttpClient;
    private final OrderRepository orderRepository;
    private final PaymentTransactionRepository paymentTransactionRepository;
    private final RefundRepository refundRepository;
    private final VaultedPaymentMethodRepository vaultedPaymentMethodRepository;


    @Override
    @Transactional
    public Map<String, Object> createOrder(BigDecimal amount, String currency, String description) {
        OrdersCreateRequest request = new OrdersCreateRequest();
        request.prefer("return=representation");
        request.requestBody(buildOrderRequest(amount, currency, description));

        try {
            HttpResponse<com.paypal.orders.Order> response = payPalHttpClient.execute(request);
            com.paypal.orders.Order paypalOrder = response.result();

            // Save order to database
            Order order = Order.builder()
                    .paypalOrderId(paypalOrder.id())
                    .amount(amount)
                    .currency(currency)
                    .status(paypalOrder.status())
                    .build();

            orderRepository.save(order);

            Map<String, Object> result = new HashMap<>();
            result.put("id", paypalOrder.id());
            result.put("status", paypalOrder.status());
            result.put("links", paypalOrder.links());

            return result;
        } catch (IOException e) {
            log.error("Error creating PayPal order", e);
            throw new RuntimeException("Failed to create PayPal order", e);
        }
    }

    @Override
    @Transactional
    public Map<String, Object> captureOrder(String orderId) {
        try {
            // First, get the order details to check its status
            Map<String, Object> orderDetails = getOrderDetails(orderId);
            String orderStatus = (String) orderDetails.get("status");

            log.info("Order {} current status: {}", orderId, orderStatus);

            // Check if order is in the correct status for capture
            if (!"APPROVED".equals(orderStatus)) {
                throw new RuntimeException("Order is not approved yet. Current status: " + orderStatus +
                    ". Please ensure the user has completed the PayPal approval process.");
            }

            OrdersCaptureRequest request = new OrdersCaptureRequest(orderId);
            request.prefer("return=representation");

            HttpResponse<com.paypal.orders.Order> response = payPalHttpClient.execute(request);
            com.paypal.orders.Order capturedOrder = response.result();

            log.info("Order {} captured successfully with status: {}", orderId, capturedOrder.status());

            // Update order in database
            Order order = orderRepository.findByPaypalOrderId(orderId)
                    .orElseThrow(() -> new RuntimeException("Order not found: " + orderId));

            order.setStatus(capturedOrder.status());
            orderRepository.save(order);

            // Save payment transaction
            if (capturedOrder.purchaseUnits().size() > 0 &&
                capturedOrder.purchaseUnits().get(0).payments() != null &&
                capturedOrder.purchaseUnits().get(0).payments().captures() != null &&
                !capturedOrder.purchaseUnits().get(0).payments().captures().isEmpty()) {

                Capture capture = capturedOrder.purchaseUnits().get(0).payments().captures().get(0);

                PaymentTransaction transaction = PaymentTransaction.builder()
                        .transactionId(capture.id())
                        .amount(new BigDecimal(capture.amount().value()))
                        .currency(capture.amount().currencyCode())
                        .status(capture.status())
                        .paymentMethod("PAYPAL")
                        .order(order)
                        .build();

                paymentTransactionRepository.save(transaction);

                log.info("Payment transaction saved: {}", capture.id());
            }

            Map<String, Object> result = new HashMap<>();
            result.put("id", capturedOrder.id());
            result.put("status", capturedOrder.status());
            result.put("purchase_units", capturedOrder.purchaseUnits());

            return result;
        } catch (IOException e) {
            log.error("Error capturing PayPal order: {}", e.getMessage(), e);

            // Check if it's the ORDER_NOT_APPROVED error
            if (e.getMessage().contains("ORDER_NOT_APPROVED")) {
                throw new RuntimeException("Order has not been approved by the user yet. " +
                    "Please ensure the user completes the PayPal payment approval process before capturing.");
            }

            throw new RuntimeException("Failed to capture PayPal order: " + e.getMessage(), e);
        }
    }







    @Override
    public Map<String, Object> getOrderDetails(String orderId) {
        // Check if this is a vault order (starts with VAULT_ or DEMO_VAULT_)
        if (orderId.startsWith("VAULT_") || orderId.startsWith("DEMO_VAULT_")) {
            return getVaultOrderDetails(orderId);
        }

        OrdersGetRequest request = new OrdersGetRequest(orderId);

        try {
            HttpResponse<com.paypal.orders.Order> response = payPalHttpClient.execute(request);
            com.paypal.orders.Order paypalOrder = response.result();

            Map<String, Object> result = new HashMap<>();
            result.put("id", paypalOrder.id());
            result.put("status", paypalOrder.status());
            result.put("create_time", paypalOrder.createTime());
            result.put("update_time", paypalOrder.updateTime());

            // 处理 purchase_units，确保包含完整信息
            List<Map<String, Object>> purchaseUnitsData = new ArrayList<>();
            if (paypalOrder.purchaseUnits() != null && !paypalOrder.purchaseUnits().isEmpty()) {
                for (PurchaseUnit unit : paypalOrder.purchaseUnits()) {
                    Map<String, Object> unitData = new HashMap<>();

                    // 添加金额信息
                    if (unit.amountWithBreakdown() != null) {
                        Map<String, Object> amountData = new HashMap<>();
                        amountData.put("currency_code", unit.amountWithBreakdown().currencyCode());
                        amountData.put("value", unit.amountWithBreakdown().value());
                        unitData.put("amount", amountData);
                    }

                    // 添加支付信息（如果存在）
                    if (unit.payments() != null) {
                        Map<String, Object> paymentsData = new HashMap<>();

                        // 添加捕获信息
                        if (unit.payments().captures() != null && !unit.payments().captures().isEmpty()) {
                            List<Map<String, Object>> capturesData = new ArrayList<>();
                            for (Capture capture : unit.payments().captures()) {
                                Map<String, Object> captureData = new HashMap<>();
                                captureData.put("id", capture.id());
                                captureData.put("status", capture.status());

                                if (capture.amount() != null) {
                                    Map<String, Object> captureAmountData = new HashMap<>();
                                    captureAmountData.put("currency_code", capture.amount().currencyCode());
                                    captureAmountData.put("value", capture.amount().value());
                                    captureData.put("amount", captureAmountData);
                                }

                                capturesData.add(captureData);
                            }
                            paymentsData.put("captures", capturesData);
                        }

                        unitData.put("payments", paymentsData);
                    }

                    purchaseUnitsData.add(unitData);
                }
            } else {
                // 如果PayPal返回的purchase_units为空，尝试从数据库获取订单信息
                Order dbOrder = orderRepository.findByPaypalOrderId(orderId).orElse(null);
                if (dbOrder != null) {
                    Map<String, Object> unitData = new HashMap<>();
                    Map<String, Object> amountData = new HashMap<>();
                    amountData.put("currency_code", dbOrder.getCurrency());
                    amountData.put("value", dbOrder.getAmount().toString());
                    unitData.put("amount", amountData);
                    purchaseUnitsData.add(unitData);

                    log.info("Used database order info for missing purchase_units data");
                }
            }

            result.put("purchase_units", purchaseUnitsData);

            // 处理 payer 信息
            Map<String, Object> payerData = new HashMap<>();
            if (paypalOrder.payer() != null) {
                if (paypalOrder.payer().name() != null) {
                    payerData.put("name", paypalOrder.payer().name());
                }
                // 注意：不同版本的PayPal SDK可能有不同的方法名
                try {
                    if (paypalOrder.payer().payerId() != null) {
                        payerData.put("payer_id", paypalOrder.payer().payerId());
                    }
                } catch (Exception e) {
                    log.debug("Could not get payer ID: {}", e.getMessage());
                }
            }
            result.put("payer", payerData);

            log.info("Order details retrieved successfully for order: {}", orderId);
            return result;
        } catch (IOException e) {
            log.error("Error getting PayPal order details for order: {}", orderId, e);
            throw new RuntimeException("Failed to get PayPal order details: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public Refund processRefund(String captureId, BigDecimal amount, String currency, String reason) {
        // Implementation for refund processing
        // This would use the PayPal Refund API
        // For now, we'll just create a mock refund record

        Order order = orderRepository.findAll().stream()
                .filter(o -> o.getTransactions().stream()
                        .anyMatch(t -> t.getTransactionId().equals(captureId)))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("No order found with capture ID: " + captureId));

        Refund refund = Refund.builder()
                .refundId("REF_" + UUID.randomUUID().toString().substring(0, 8))
                .amount(amount)
                .currency(currency)
                .status("COMPLETED")
                .reason(reason)
                .order(order)
                .build();

        return refundRepository.save(refund);
    }

    @Override
    @Transactional
    public VaultedPaymentMethod vaultPaymentMethod(String customerId, String paymentMethodId,
                                                  String paymentType, String lastFour,
                                                  String brand, boolean isDefault) {
        // If this is set as default, unset any existing default
        if (isDefault) {
            vaultedPaymentMethodRepository.findByCustomerIdAndIsDefaultTrue(customerId)
                    .ifPresent(existingDefault -> {
                        existingDefault.setDefault(false);
                        vaultedPaymentMethodRepository.save(existingDefault);
                    });
        }

        VaultedPaymentMethod paymentMethod = VaultedPaymentMethod.builder()
                .paymentMethodId(paymentMethodId)
                .paymentType(paymentType)
                .customerId(customerId)
                .lastFour(lastFour)
                .brand(brand)
                .isDefault(isDefault)
                .build();

        return vaultedPaymentMethodRepository.save(paymentMethod);
    }

    @Override
    public List<VaultedPaymentMethod> getVaultedPaymentMethods(String customerId) {
        return vaultedPaymentMethodRepository.findByCustomerId(customerId);
    }

    @Override
    public List<Order> getAllOrders() {
        return orderRepository.findAll();
    }

    @Override
    public Order getOrderByPayPalOrderId(String paypalOrderId) {
        return orderRepository.findByPaypalOrderId(paypalOrderId)
                .orElseThrow(() -> new RuntimeException("Order not found: " + paypalOrderId));
    }

    private OrderRequest buildOrderRequest(BigDecimal amount, String currency, String description) {
        OrderRequest orderRequest = new OrderRequest();
        orderRequest.checkoutPaymentIntent("CAPTURE");

        List<PurchaseUnitRequest> purchaseUnits = new ArrayList<>();
        PurchaseUnitRequest purchaseUnit = new PurchaseUnitRequest()
                .description(description)
                .amountWithBreakdown(new AmountWithBreakdown()
                        .currencyCode(currency)
                        .value(amount.toString()));

        purchaseUnits.add(purchaseUnit);
        orderRequest.purchaseUnits(purchaseUnits);

        ApplicationContext applicationContext = new ApplicationContext()
                .brandName("PayPal Demo")
                .landingPage("LOGIN")
                .userAction("PAY_NOW")
                .returnUrl("http://localhost:3000/success")
                .cancelUrl("http://localhost:3000/checkout");

        orderRequest.applicationContext(applicationContext);

        return orderRequest;
    }



    /**
     * Get vault order details from database
     */
    private Map<String, Object> getVaultOrderDetails(String orderId) {
        try {
            Order order = orderRepository.findByPaypalOrderId(orderId)
                    .orElseThrow(() -> new RuntimeException("Vault order not found: " + orderId));

            Map<String, Object> result = new HashMap<>();
            result.put("id", order.getPaypalOrderId());
            result.put("status", order.getStatus());
            result.put("create_time", order.getCreatedAt().toString());
            result.put("update_time", order.getUpdatedAt().toString());

            // Build purchase units from database data
            Map<String, Object> purchaseUnit = new HashMap<>();
            Map<String, Object> amountData = new HashMap<>();
            amountData.put("value", order.getAmount().toString());
            amountData.put("currency_code", order.getCurrency());
            purchaseUnit.put("amount", amountData);

            // Add payment/capture info if available
            if (!order.getTransactions().isEmpty()) {
                PaymentTransaction transaction = order.getTransactions().get(0);
                Map<String, Object> payments = new HashMap<>();
                Map<String, Object> capture = new HashMap<>();
                capture.put("id", transaction.getTransactionId());
                capture.put("status", transaction.getStatus());
                capture.put("amount", amountData);
                payments.put("captures", List.of(capture));
                purchaseUnit.put("payments", payments);
            }

            result.put("purchase_units", List.of(purchaseUnit));
            result.put("payer", new HashMap<>());

            log.info("Vault order details retrieved successfully for order: {}", orderId);
            return result;
        } catch (Exception e) {
            log.error("Error getting vault order details for order: {}", orderId, e);
            throw new RuntimeException("Failed to get vault order details: " + e.getMessage(), e);
        }
    }
}
