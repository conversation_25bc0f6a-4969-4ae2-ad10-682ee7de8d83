package com.example.paypal.controller;

import com.example.paypal.model.Order;
import com.example.paypal.model.Refund;
import com.example.paypal.model.VaultedPaymentMethod;
import com.example.paypal.service.PayPalService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/paypal")
@RequiredArgsConstructor
@Slf4j
public class PayPalController {

    private final PayPalService payPalService;

    @PostMapping("/create-order")
    public ResponseEntity<Map<String, Object>> createOrder(@RequestBody Map<String, Object> request) {
        try {
            BigDecimal amount = new BigDecimal(request.get("amount").toString());
            String currency = request.getOrDefault("currency", "USD").toString();
            String description = request.getOrDefault("description", "PayPal Demo Order").toString();

            Map<String, Object> orderResult = payPalService.createOrder(amount, currency, description);

            return ResponseEntity.ok(orderResult);
        } catch (Exception e) {
            log.error("Error creating order", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }



    @PostMapping("/capture-order")
    public ResponseEntity<Map<String, Object>> captureOrder(@RequestBody Map<String, String> request) {
        try {
            String orderId = request.get("orderId");

            if (orderId == null || orderId.isEmpty()) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("error", "Order ID is required");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            log.info("Attempting to capture order: {}", orderId);

            Map<String, Object> captureResult = payPalService.captureOrder(orderId);

            log.info("Order captured successfully: {}", orderId);
            return ResponseEntity.ok(captureResult);
        } catch (Exception e) {
            log.error("Error capturing order: {}", e.getMessage(), e);

            Map<String, Object> errorResponse = new HashMap<>();

            // 处理特定的PayPal错误
            if (e.getMessage().contains("ORDER_NOT_APPROVED")) {
                errorResponse.put("error", "Order has not been approved by the user yet. Please ensure the user completes the PayPal payment approval process.");
                errorResponse.put("errorCode", "ORDER_NOT_APPROVED");
            } else if (e.getMessage().contains("not approved")) {
                errorResponse.put("error", "Order is not approved yet. Current status may not allow capture.");
                errorResponse.put("errorCode", "NOT_APPROVED");
            } else {
                errorResponse.put("error", e.getMessage());
            }

            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    @GetMapping("/order-details/{orderId}")
    public ResponseEntity<Map<String, Object>> getOrderDetails(@PathVariable String orderId) {
        try {
            Map<String, Object> orderDetails = payPalService.getOrderDetails(orderId);

            return ResponseEntity.ok(orderDetails);
        } catch (Exception e) {
            log.error("Error getting order details", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    @PostMapping("/refund")
    public ResponseEntity<Map<String, Object>> processRefund(@RequestBody Map<String, Object> request) {
        try {
            String orderId = request.get("orderId").toString();
            BigDecimal amount = new BigDecimal(request.get("amount").toString());
            String currency = request.getOrDefault("currency", "USD").toString();
            String reason = request.getOrDefault("reason", "Customer requested").toString();

            Refund refund = payPalService.processRefund(orderId, amount, currency, reason);

            Map<String, Object> response = new HashMap<>();
            response.put("refundId", refund.getRefundId());
            response.put("status", refund.getStatus());
            response.put("amount", refund.getAmount());
            response.put("currency", refund.getCurrency());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error processing refund", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    @PostMapping("/vault-payment-method")
    public ResponseEntity<Map<String, Object>> vaultPaymentMethod(@RequestBody Map<String, Object> request) {
        try {
            String customerId = request.get("customerId").toString();
            String paymentMethodId = request.get("paymentMethodId").toString();
            String paymentType = request.get("paymentType").toString();
            String lastFour = request.get("lastFour").toString();
            String brand = request.get("brand").toString();
            boolean isDefault = Boolean.parseBoolean(request.getOrDefault("isDefault", "false").toString());

            VaultedPaymentMethod paymentMethod = payPalService.vaultPaymentMethod(
                    customerId, paymentMethodId, paymentType, lastFour, brand, isDefault);

            Map<String, Object> response = new HashMap<>();
            response.put("id", paymentMethod.getId());
            response.put("paymentMethodId", paymentMethod.getPaymentMethodId());
            response.put("paymentType", paymentMethod.getPaymentType());
            response.put("lastFour", paymentMethod.getLastFour());
            response.put("brand", paymentMethod.getBrand());
            response.put("isDefault", paymentMethod.isDefault());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error vaulting payment method", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    @GetMapping("/orders")
    public ResponseEntity<List<Map<String, Object>>> getAllOrders() {
        try {
            List<Order> orders = payPalService.getAllOrders();

            List<Map<String, Object>> response = orders.stream()
                    .map(order -> {
                        Map<String, Object> map = new HashMap<>();
                        map.put("id", order.getId());
                        map.put("paypalOrderId", order.getPaypalOrderId());
                        map.put("amount", order.getAmount());
                        map.put("currency", order.getCurrency());
                        map.put("status", order.getStatus());
                        map.put("createdAt", order.getCreatedAt());

                        // Add refund info if available
                        if (!order.getRefunds().isEmpty()) {
                            map.put("refunded", true);
                            map.put("refundAmount", order.getRefunds().stream()
                                    .map(Refund::getAmount)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add));
                        } else {
                            map.put("refunded", false);
                        }

                        return map;
                    })
                    .collect(Collectors.toList());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error getting all orders", e);
            return ResponseEntity.badRequest().body(new ArrayList<>());
        }
    }
}
